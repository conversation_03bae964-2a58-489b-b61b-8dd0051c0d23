import { ChevronDown } from "lucide-react";
import { useMemo, useState } from "react";
import { Select } from "../ui/select";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";

export interface PrismData {
  rightEye: {
    vertical: string;
    verticalBase: string;
    horizontal: string;
    horizontalBase: string;
  };
  leftEye: {
    vertical: string;
    verticalBase: string;
    horizontal: string;
    horizontalBase: string;
  };
}

interface MoreOptionProps {
  register: any;
  setValue: any;
  errors: any;
  watch: any;
  onPrismChange?: (hasPrism: boolean, prismData?: PrismData) => void;
}

export const MoreOption: React.FC<MoreOptionProps> = ({
  register,
  setValue,
  errors,
  watch,
  onPrismChange,
}) => {
  const [moreOptionOpen, setMoreOptionOpen] = useState(false);
  const [showPrismTable, setShowPrismTable] = useState(false);
  const [prismData, setPrismData] = useState<PrismData>({
    rightEye: {
      vertical: "n/a",
      verticalBase: "n/a",
      horizontal: "n/a",
      horizontalBase: "n/a",
    },
    leftEye: {
      vertical: "n/a",
      verticalBase: "n/a",
      horizontal: "n/a",
      horizontalBase: "n/a",
    },
  });

  // Prism values for the dropdowns

  const prismValues = useMemo(() => {
    const values = [];
    values.push({ label: "n/a", value: "n/a" });
    for (let i = 0.5; i <= 4.0; i += 0.5) {
      const prismValue = parseFloat(i.toFixed(1));
      values.push({
        label: prismValue.toString(),
        value: prismValue.toString(),
      });
    }
    return values;
  }, []);

  const verticalBaseValues = [
    { label: "n/a", value: "n/a" },
    { label: "Up", value: "Up" },
    { label: "Down", value: "Down" },
  ];

  const horizontalBaseValues = [
    { label: "n/a", value: "n/a" },
    { label: "In", value: "In" },
    { label: "Out", value: "Out" },
  ];

  const handlePrismChange = (
    eye: "rightEye" | "leftEye",
    field: string,
    value: string
  ) => {
    const updatedPrismData = {
      ...prismData,
      [eye]: {
        ...prismData[eye],
        [field]: value,
      },
    };
    setPrismData(updatedPrismData);

    // Notify parent component about prism changes
    if (onPrismChange) {
      onPrismChange(showPrismTable, updatedPrismData);
    }
  };

  const handlePrismToggle = (checked: boolean) => {
    setShowPrismTable(checked);

    // Reset prism data when unchecked
    if (!checked) {
      const resetPrismData = {
        rightEye: {
          vertical: "n/a",
          verticalBase: "n/a",
          horizontal: "n/a",
          horizontalBase: "n/a",
        },
        leftEye: {
          vertical: "n/a",
          verticalBase: "n/a",
          horizontal: "n/a",
          horizontalBase: "n/a",
        },
      };
      setPrismData(resetPrismData);
      if (onPrismChange) {
        onPrismChange(false, resetPrismData);
      }
    } else {
      if (onPrismChange) {
        onPrismChange(true, prismData);
      }
    }
  };

  // Validation function to check if all prism fields are filled when prism is selected
  const validatePrismData = () => {
    if (!showPrismTable) return true;

    const { rightEye, leftEye } = prismData;

    // Check if any prism value is selected (not n/a)
    const hasVerticalPrism =
      rightEye.vertical !== "n/a" || leftEye.vertical !== "n/a";
    const hasHorizontalPrism =
      rightEye.horizontal !== "n/a" || leftEye.horizontal !== "n/a";

    if (!hasVerticalPrism && !hasHorizontalPrism) {
      return false; // No prism values selected
    }

    // If vertical prism is selected, base direction must be selected
    if (rightEye.vertical !== "n/a" && rightEye.verticalBase === "n/a")
      return false;
    if (leftEye.vertical !== "n/a" && leftEye.verticalBase === "n/a")
      return false;

    // If horizontal prism is selected, base direction must be selected
    if (rightEye.horizontal !== "n/a" && rightEye.horizontalBase === "n/a")
      return false;
    if (leftEye.horizontal !== "n/a" && leftEye.horizontalBase === "n/a")
      return false;

    return true;
  };
  return (
    <div className="flex flex-col mt-5">
      <h4
        onClick={() => setMoreOptionOpen(!moreOptionOpen)}
        className="flex items-center cursor-pointer underline text-gray-700"
      >
        More Options{" "}
        <span
          className={`inline-block transform transition-transform duration-300 ${
            moreOptionOpen ? "rotate-180" : "rotate-0"
          }`}
        >
          <ChevronDown size={22} />
        </span>
      </h4>
      <div
        className={`flex mt-3 flex-col gap-2 ${
          moreOptionOpen
            ? "max-h-96 opacity-100"
            : "max-h-0 opacity-0 overflow-hidden"
        } transition-all duration-300`}
      >
        <div className="flex gap-3 items-center">
          <Checkbox
            id="add-prism"
            checked={showPrismTable}
            onCheckedChange={(checked) => handlePrismToggle(!!checked)}
          />
          <label htmlFor="add-prism" className="cursor-pointer">
            Add prism <span className="font-semibold">$15</span>
          </label>
        </div>

        {showPrismTable && !validatePrismData() && (
          <p className="text-sm text-red-500 mt-1">
            Please select prism values and base directions for at least one eye.
          </p>
        )}

        {/* Prism Tables */}
        <div className={`mt-3 ${showPrismTable ? "block" : "hidden"}`}>
          {/* Vertical Prism Table */}
          <div className="mb-4">
            <h5 className="font-medium mb-2 text-sm">Vertical Prism (Δ)</h5>
            <div className="border rounded-md overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50 text-sm">
                  <tr>
                    <th className="py-2 px-3 text-left font-medium text-gray-700"></th>
                    <th className="py-2 px-3 text-center font-medium text-gray-700">
                      Vertical
                    </th>
                    <th className="py-2 px-3 text-center font-medium text-gray-700">
                      Base Direction
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t">
                    <td className="py-2 px-3 text-sm text-gray-700">
                      <div className="flex flex-col">
                        <span className="font-medium">OD</span>
                        <span className="text-xs text-gray-500">right eye</span>
                      </div>
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={prismValues}
                        value={prismData.rightEye.vertical}
                        onChange={(e) =>
                          handlePrismChange(
                            "rightEye",
                            "vertical",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={verticalBaseValues}
                        value={prismData.rightEye.verticalBase}
                        onChange={(e) =>
                          handlePrismChange(
                            "rightEye",
                            "verticalBase",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                  </tr>
                  <tr className="border-t">
                    <td className="py-2 px-3 text-sm text-gray-700">
                      <div className="flex flex-col">
                        <span className="font-medium">OS</span>
                        <span className="text-xs text-gray-500">left eye</span>
                      </div>
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={prismValues}
                        value={prismData.leftEye.vertical}
                        onChange={(e) =>
                          handlePrismChange(
                            "leftEye",
                            "vertical",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={verticalBaseValues}
                        value={prismData.leftEye.verticalBase}
                        onChange={(e) =>
                          handlePrismChange(
                            "leftEye",
                            "verticalBase",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Horizontal Prism Table */}
          <div className="mb-4">
            <h5 className="font-medium mb-2 text-sm">Horizontal Prism (Δ)</h5>
            <div className="border rounded-md overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50 text-sm">
                  <tr>
                    <th className="py-2 px-3 text-left font-medium text-gray-700"></th>
                    <th className="py-2 px-3 text-center font-medium text-gray-700">
                      Horizontal
                    </th>
                    <th className="py-2 px-3 text-center font-medium text-gray-700">
                      Base Direction
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t">
                    <td className="py-2 px-3 text-sm text-gray-700">
                      <div className="flex flex-col">
                        <span className="font-medium">OD</span>
                        <span className="text-xs text-gray-500">right eye</span>
                      </div>
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={prismValues}
                        value={prismData.rightEye.horizontal}
                        onChange={(e) =>
                          handlePrismChange(
                            "rightEye",
                            "horizontal",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={horizontalBaseValues}
                        value={prismData.rightEye.horizontalBase}
                        onChange={(e) =>
                          handlePrismChange(
                            "rightEye",
                            "horizontalBase",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                  </tr>
                  <tr className="border-t">
                    <td className="py-2 px-3 text-sm text-gray-700">
                      <div className="flex flex-col">
                        <span className="font-medium">OS</span>
                        <span className="text-xs text-gray-500">left eye</span>
                      </div>
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={prismValues}
                        value={prismData.leftEye.horizontal}
                        onChange={(e) =>
                          handlePrismChange(
                            "leftEye",
                            "horizontal",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                    <td className="py-2 px-3">
                      <Select
                        options={horizontalBaseValues}
                        value={prismData.leftEye.horizontalBase}
                        onChange={(e) =>
                          handlePrismChange(
                            "leftEye",
                            "horizontalBase",
                            e.target.value
                          )
                        }
                        className="w-full"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div className="flex flex-col">
          <p className="my-1 font-medium">Extra information</p>
          <Textarea
            {...register("extraInformation")}
            placeholder="Enter any additional information about your prescription..."
            className="min-h-[80px]"
          />
        </div>
      </div>
      <div className="flex flex-col mt-2">
        <div className="flex gap-2 bg-white">
          <Checkbox
            id="terms-accepted"
            {...register("termsAccepted")}
            checked={watch("termsAccepted")}
            onCheckedChange={(checked) => {
              setValue("termsAccepted", checked === true, {
                shouldValidate: true,
              });
            }}
          />
          <label htmlFor="terms-accepted" className="text-xs text-gray-700">
            <span className="text-red-500">* </span>I confirm that I've read and
            agree to the{" "}
            <span className="underline font-medium text-blue-600 cursor-pointer">
              Terms and Conditions
            </span>
            and that the prescription used is less than two years old. I certify
            that the wearer is over 16 years old and that they are not
            registered blind or partially sighted. I also confirm that the
            prescription details above have been entered correctly and I am
            happy that no errors have been made.
          </label>
        </div>
        {errors.termsAccepted && (
          <p className="text-sm text-red-500 mt-1 ml-6">
            {errors.termsAccepted.message}
          </p>
        )}
      </div>
    </div>
  );
};
