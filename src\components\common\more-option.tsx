import { ChevronDown } from "lucide-react";
import { useMemo, useState } from "react";
import { Select } from "../ui/select";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";



interface MoreOptionProps {
  register: any;
  setValue: any;
  errors: any;
  watch: any;
}

export const MoreOption: React.FC<MoreOptionProps> = ({
  register,
  setValue,
  errors,
  watch,
}) => {
  const [moreOptionOpen, setMoreOptionOpen] = useState(false);
  const [showPrismTable, setShowPrismTable] = useState(false);
  const [prismData, setPrismData] = useState({
    rightEye: { sph: '0.00', cyl: '0.00', axis: '--', add: 'n/a' },
    leftEye: { sph: '0.00', cyl: '0.00', axis: '--', add: 'n/a' },
  });

  // Use the same values as in the main prescription form
  const sphValues = useMemo(() => {
    const values = [];
    for (let i = -9.75; i <= 9.75; i += 0.25) {
      const sphValue = parseFloat(i.toFixed(2));
      values.push({ label: sphValue.toString(), value: sphValue.toString() });
      if (sphValue === 0) {
        values.push({ label: 'Plano', value: 'Plano' });
      }
    }
    return values;
  }, []);

  const cylValues = useMemo(() => {
    const values = [];
    for (let i = -4.0; i <= 4.0; i += 0.25) {
      const cylValue = parseFloat(i.toFixed(2));
      values.push({ label: cylValue.toString(), value: cylValue.toString() });
      if (cylValue === 0) {
        values.push({ label: 'SPH/DS', value: 'SPH/DS' });
      }
    }
    return values;
  }, []);

  const addValues = useMemo(() => {
    const values = [];
    values.push({ label: 'n/a', value: 'n/a' });
    for (let i = 0.75; i <= 3.5; i += 0.25) {
      const addValue = parseFloat(i.toFixed(2));
      values.push({ label: addValue.toString(), value: addValue.toString() });
    }
    return values;
  }, []);

  const handlePrismChange = (
    eye: 'rightEye' | 'leftEye',
    field: string,
    value: string
  ) => {
    setPrismData((prev) => ({
      ...prev,
      [eye]: {
        ...prev[eye],
        [field]: value,
      },
    }));
  };
  // console.log("prismData",prismData);
  return (
    <div className="flex flex-col mt-5">
      <h4
        onClick={() => setMoreOptionOpen(!moreOptionOpen)}
        className="flex items-center cursor-pointer underline text-gray-700"
      >
        More Options{' '}
        <span
          className={`inline-block transform transition-transform duration-300 ${
            moreOptionOpen ? 'rotate-180' : 'rotate-0'
          }`}
        >
          <ChevronDown size={22} />
        </span>
      </h4>
      <div
        className={`flex mt-3 flex-col gap-2 ${
          moreOptionOpen
            ? 'max-h-96 opacity-100'
            : 'max-h-0 opacity-0 overflow-hidden'
        } transition-all duration-300`}
      >
        <div className="flex gap-3 items-center">
          <Checkbox
            id="add-prism"
            onCheckedChange={(checked) => setShowPrismTable(!!checked)}
          />
          <label htmlFor="add-prism" className="cursor-pointer">
            Add prism <span className="font-semibold">$15</span>
          </label>
        </div>

        {/* Prism Table */}
        <div className={`mt-3 ${showPrismTable ? 'block' : 'hidden'}`}>
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50 text-sm">
                <tr>
                  <th className="py-2 px-3 text-left font-medium text-gray-700"></th>
                  <th className="py-2 px-3 text-center font-medium text-gray-700">
                    SPH
                  </th>
                  <th className="py-2 px-3 text-center font-medium text-gray-700">
                    CYL
                  </th>
                  <th className="py-2 px-3 text-center font-medium text-gray-700">
                    Axis
                  </th>
                  <th className="py-2 px-3 text-center font-medium text-gray-700">
                    ADD
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t">
                  <td className="py-2 px-3 text-sm text-gray-700">
                    <div className="flex flex-col">
                      <span className="font-medium">OD</span>
                      <span className="text-xs text-gray-500">right eye</span>
                    </div>
                  </td>
                  <td className="py-2 px-3">
                    <Select
                      options={sphValues}
                      value={prismData.rightEye.sph}
                      onChange={(e) =>
                        handlePrismChange('rightEye', 'sph', e.target.value)
                      }
                      className="w-full"
                    />
                  </td>
                  <td className="py-2 px-3">
                    <Select
                      options={cylValues}
                      value={prismData.rightEye.cyl}
                      onChange={(e) =>
                        handlePrismChange('rightEye', 'cyl', e.target.value)
                      }
                      className="w-full"
                    />
                  </td>
                  <td className="py-2 px-3 text-center">--</td>
                  <td className="py-2 px-3">
                    <Select
                      options={addValues}
                      value={prismData.rightEye.add}
                      onChange={(e) =>
                        handlePrismChange('rightEye', 'add', e.target.value)
                      }
                      className="w-full"
                    />
                  </td>
                </tr>
                <tr className="border-t">
                  <td className="py-2 px-3 text-sm text-gray-700">
                    <div className="flex flex-col">
                      <span className="font-medium">OS</span>
                      <span className="text-xs text-gray-500">left eye</span>
                    </div>
                  </td>
                  <td className="py-2 px-3">
                    <Select
                      options={sphValues}
                      value={prismData.leftEye.sph}
                      onChange={(e) =>
                        handlePrismChange('leftEye', 'sph', e.target.value)
                      }
                      className="w-full"
                    />
                  </td>
                  <td className="py-2 px-3">
                    <Select
                      options={cylValues}
                      value={prismData.leftEye.cyl}
                      onChange={(e) =>
                        handlePrismChange('leftEye', 'cyl', e.target.value)
                      }
                      className="w-full"
                    />
                  </td>
                  <td className="py-2 px-3 text-center">--</td>
                  <td className="py-2 px-3">
                    <Select
                      options={addValues}
                      value={prismData.leftEye.add}
                      onChange={(e) =>
                        handlePrismChange('leftEye', 'add', e.target.value)
                      }
                      className="w-full"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className="flex flex-col">
          <p className="my-1 font-medium">Extra information</p>
          <Textarea />
        </div>
      </div>
      <div className="flex flex-col mt-2">
        <div className="flex gap-2 bg-white">
          <Checkbox
            id="terms-accepted"
            {...register('termsAccepted')}
            checked={watch('termsAccepted')}
            onCheckedChange={(checked) => {
              setValue('termsAccepted', checked === true, {
                shouldValidate: true,
              });
            }}
          />
          <label htmlFor="terms-accepted" className="text-xs text-gray-700">
            <span className="text-red-500">* </span>I confirm that I've read and
            agree to the{' '}
            <span className="underline font-medium text-blue-600 cursor-pointer">
              Terms and Conditions
            </span>
            and that the prescription used is less than two years old. I certify
            that the wearer is over 16 years old and that they are not
            registered blind or partially sighted. I also confirm that the
            prescription details above have been entered correctly and I am
            happy that no errors have been made.
          </label>
        </div>
        {errors.termsAccepted && (
          <p className="text-sm text-red-500 mt-1 ml-6">
            {errors.termsAccepted.message}
          </p>
        )}
      </div>
    </div>
  );
};
