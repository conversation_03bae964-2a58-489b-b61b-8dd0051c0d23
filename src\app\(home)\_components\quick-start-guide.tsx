import { Wrapper } from "@/components/common/wrapper";
import Image from "next/image";
import React from "react";

export const QuickStartGuide = () => {
  const data = [
    "/quick-start/1.png",
    "/quick-start/2.png",
    "/quick-start/3.png",
  ];
  return (
    <Wrapper>
      <p className="font-semibold text-center text-2xl mb-4">Quick Start Guide</p>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-5">
        {data.map((dt, index) => (
          <div key={index}>
            <Image
              src={dt}
              alt="quick start guide"
              width={700}
              height={700}
              className="w-full"
            />
          </div>
        ))}
      </div>
    </Wrapper>
  );
};
