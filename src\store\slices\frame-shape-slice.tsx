// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IFrameShape, IFrameShapeSlice } from "@/interfaces";

// CONSTANTS
import { FRAME_SHAPE_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: IFrameShapeSlice = {
  frameShapes: [],
  loading: true,
  error: "",
};

export const fetchFrameShapes = createAsyncThunk<
  IFrameShape[],
  void,
  FetchDataRejected
>("frameShape/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(FRAME_SHAPE_URL + `?page=1&limit=100`)
    .then((res) => {
      return res.data.data.data;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const frameShapeSlice = createSlice({
  name: "frameShape",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchFrameShapes.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchFrameShapes.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.frameShapes = payload;
    });
    builder.addCase(fetchFrameShapes.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default frameShapeSlice.reducer;
