"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Eye, EyeOff, MoveRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { signIn } from "next-auth/react";
import { toast } from "sonner";

// NEXT
import { useRouter } from "next/navigation";

const loginSchema = z.object({
  email: z.string().email({ message: "Invalid email" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" }),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (values: LoginFormData) => {
    try {
      const result = await signIn("credentials", {
        username: values.email,
        password: values.password,
        redirect: false,
      });

      if (result?.error) {
        toast("Error", {
          description: result?.error || "Something went wrong",
          descriptionClassName: "description-text",
        });
      } else {
        toast("Success", {
          description: "Logged in successfully",
          descriptionClassName: "description-text",
        });
        router.refresh();
      }
    } catch (error) {
      toast("Error", {
        description: "Something went wrong",
        descriptionClassName: "description-text",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Email Address</label>

        <Input
          type="email"
          placeholder="<EMAIL>"
          {...register("email")}
          className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm"
        />

        {errors.email && (
          <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
        )}
      </div>

      <div className="mb-6">
        <label className="flex justify-between text-sm font-medium mb-2">
          Password
          <Link className="text-blue-400" href={"/auth/forgot-password"}>
            Forgot Password
          </Link>
        </label>

        <div className="relative">
          <Input
            type={showPassword ? "text" : "password"}
            {...register("password")}
            placeholder="••••••••"
            className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
          />

          <Button
            type="button"
            size={"icon"}
            className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
            onClick={() => setShowPassword((prev) => !prev)}
          >
            {showPassword ? (
              <EyeOff className="w-4 h-4" />
            ) : (
              <Eye className="w-4 h-4" />
            )}
          </Button>
        </div>

        {errors.password && (
          <p className="text-sm text-red-500 mt-1">{errors.password.message}</p>
        )}
      </div>

      <Button disabled={isSubmitting} size={"lg"} className="w-full py-4">
        {isSubmitting ? (
          "Logging in..."
        ) : (
          <>
            Login <MoveRight />{" "}
          </>
        )}
      </Button>

      {/* <Separator text="OR" /> */}

      {/* <div className="space-y-3">
        <Button
          type="button"
          className="w-full flex items-center justify-center gap-2 transition bg-white text-text-accent shadow-none border border-gray-200 h-11 hover:bg-gray-50 hover:text-text-accent"
        >
          <img src="/google.svg" alt="Google" className="w-5 h-5" />
          Continue with Google
        </Button>

        <Button
          type="button"
          className="w-full flex items-center justify-center gap-2 transition bg-white text-text-accent shadow-none border border-gray-200 h-11 hover:bg-gray-50 hover:text-text-accent"
        >
          <img src="/apple.svg" alt="Apple" className="w-5 h-5" />
          Continue with Apple
        </Button>
      </div> */}
    </form>
  );
}
