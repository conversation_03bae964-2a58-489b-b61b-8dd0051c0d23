"use client";

import { cn } from "@/lib/utils";
import { Minus, Plus } from "lucide-react";

interface QuantityInputProps {
  quantity: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  containerClass?: string;
  buttonClass?: string;
}

export default function QuantityInput({
  quantity,
  onChange,
  min = 1,
  max = 99,
  containerClass,
  buttonClass,
}: QuantityInputProps) {
  const increase = () => {
    if (quantity < max) onChange(quantity + 1);
  };

  const decrease = () => {
    if (quantity > min) onChange(quantity - 1);
  };

  return (
    <div
      className={cn(
        "flex items-center gap-2 border border-gray-300 rounded-md px-2 py-1 w-fit",
        containerClass
      )}
    >
      <button
        onClick={decrease}
        className={cn(
          "p-1 hover:bg-gray-100 rounded disabled:opacity-50",
          buttonClass
        )}
        disabled={quantity <= min}
        type="button"
      >
        <Minus className="w-4 h-4" />
      </button>

      <span className="w-6 text-center text-sm">{quantity}</span>

      <button
        onClick={increase}
        className={cn(
          "p-1 hover:bg-gray-100 rounded disabled:opacity-50",
          buttonClass
        )}
        disabled={quantity >= max}
        type="button"
      >
        <Plus className="w-4 h-4" />
      </button>
    </div>
  );
}
