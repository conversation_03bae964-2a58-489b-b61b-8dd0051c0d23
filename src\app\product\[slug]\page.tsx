"use client";
import { useParams } from "next/navigation";
import React, { useState, useEffect } from "react";
import { ProductMedia } from "./_components/product-media";
import { formatPrice } from "@/utils/format-price";
import { Button } from "@/components/ui/button";
import { Heart } from "lucide-react";
import { cn } from "@/lib/utils";
import { IProduct } from "@/interfaces";
import { api_interceptor } from "@/utils/api-interceptor";
import { PRODUCT_URL } from "@/constants/url";
import { IVariation } from "@/interfaces/variations";
import store, { RootState } from "@/store/store";
import { addToCart } from "@/store/slices/cart-slice";
import { toast } from "sonner";
import {
  LensWizard,
  LensSelectionData,
} from "@/components/product/lens-selection/lens-wizard";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useSelector } from "react-redux";
import {
  addToWishlist,
  fetchWishlist,
  removeFromWishlist,
} from "@/store/slices/wishlist-slice";
import { Wrapper } from "@/components/common/wrapper";
import { RelatedProducts } from "./_components/related-products";

const page = () => {
  const { slug } = useParams();
  const { user } = useSelector((state: RootState) => state.user);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [product, setProduct] = useState<IProduct | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [selectedVariation, setSelectedVariation] = useState<IVariation | null>(
    null
  );
  const [selectedAttributes, setSelectedAttributes] = useState<
    Record<string, string>
  >({});

  const [favorite, setFavorite] = useState<boolean>(false);
  const [isLensWizardOpen, setIsLensWizardOpen] = useState<boolean>(false);
  const [thumbnail, setThumbnail] = useState("");

  const toggleFavorite = () => {
    if (!user) {
      toast.error("Error", {
        description: "Please login to add product to wishlist",
        descriptionClassName: "description-text",
      });
      return;
    }
    if (!favorite) {
      store
        .dispatch(addToWishlist({ productId: product?._id ?? "" }))
        .then((res: any) => {
          if (res.meta.requestStatus === "rejected") {
            toast.error("Error", {
              description: res.payload.message,
              descriptionClassName: "description-text",
            });
          } else {
            toast.success("Success", {
              description: "Product added to wishlist",
              descriptionClassName: "description-text",
            });
            store.dispatch(fetchWishlist({}));
          }
        });
    } else {
      store
        .dispatch(removeFromWishlist({ productId: product?._id ?? "" }))
        .then((res: any) => {
          if (res.meta.requestStatus === "rejected") {
            toast.error("Error", {
              description: res.payload.message,
              descriptionClassName: "description-text",
            });
          } else {
            toast.success("Success", {
              description: "Product removed from wishlist",
              descriptionClassName: "description-text",
            });
          }
        });
    }

    setFavorite(!favorite);
  };

  const openLensWizard = () => {
    setIsLensWizardOpen(true);
  };

  const closeLensWizard = () => {
    setIsLensWizardOpen(false);
  };

  useEffect(() => {
    api_interceptor.get(PRODUCT_URL + `/${slug}`).then((res) => {
      setProduct(res.data.data);
      const attributes: Record<string, string> = {};

      if (res.data?.data?.thumbnail) setThumbnail(res.data?.data?.thumbnail);

      if (res.data.data.variations.length > 0) {
        Object.keys(res.data.data.variations[0].attributes).forEach((key) => {
          const parts = key.split("-");
          const mainKey = parts[0];
          const value = parts.slice(1).join("-");
          attributes[mainKey] = value;
        });
        setSelectedVariation(res.data.data.variations[0]);
        setSelectedAttributes(attributes);
      }
    });
    return () => {};
  }, [slug]);

  const onAddToCart = (lensData?: LensSelectionData) => {
    if (!product) return;

    const cartProduct = JSON.parse(JSON.stringify(product));
    delete cartProduct.variations;

    store.dispatch(
      addToCart({
        product: cartProduct,
        quantity,
        variation: selectedVariation || undefined,
        lensData: lensData as any,
      })
    );

    toast("Success", {
      description: lensData
        ? "Product with custom lenses added to cart"
        : "Product added to cart",
      descriptionClassName: "description-text",
    });
  };

  const handleAddToCartWithLenses = (
    product: IProduct,
    lensData: LensSelectionData
  ) => {
    onAddToCart(lensData);
  };

  return (
    <Wrapper vertical className="">
      <div>
        <div className="md:grid md:grid-cols-7 space-y-4 md:space-y-0 md:gap-8">
          <div className="md:col-span-4 w-full sticky">
            <ProductMedia
              gallery={product?.images || []}
              thumbnail={thumbnail || ""}
              slug={product?.slug || ""}
              setThumbnail={setThumbnail}
            />
          </div>

          <div className="md:col-span-3 md:w-11/12">
            <h1 className="text-xl md:text-2xl font-medium">{product?.name}</h1>
            <p className="font-semibold text-gray-600">
              Brand: {product?.brand.name}
            </p>

            <div className="space-y-3">
              <p className="text-gray-700">{product?.shortDescription}</p>

              <div className="space-y-2">
                {Object.keys(product?.attributes || {}).map((variation) => (
                  <div key={variation} className="space-y-2">
                    <h2 className="text-sm font-semibold text-gray-700">
                      {variation}
                    </h2>
                    <div className="flex flex-wrap gap-3">
                      {product?.attributes[variation].map((option) => {
                        const currentOption = option
                          .toLowerCase()
                          .replace(/ /g, "-")
                          .replace(/>=/g, "greater");
                        const currentVariation = variation.toLowerCase();
                        const currentCombination = {
                          ...selectedAttributes,
                          [currentVariation]: currentOption,
                        };

                        const combinedCurrentCombination = Object.entries(
                          currentCombination
                        ).map(([key, value]) => `${key}-${value}`);

                        const isMatched = product.variations.find((entry) => {
                          return combinedCurrentCombination.every((key) =>
                            Object.keys(entry.attributes).some((k) =>
                              k.includes(key)
                            )
                          );
                        });

                        return (
                          <button
                            disabled={!isMatched}
                            onClick={() => {
                              setSelectedAttributes({
                                ...selectedAttributes,
                                [currentVariation]: currentOption,
                              });
                              if (isMatched) {
                                console.log("changing variation");
                                setSelectedVariation(isMatched);
                              }
                            }}
                            key={option}
                            className={cn(
                              "px-3 py-2 text-sm font-medium text-gray-600 border border-gray-300 transition-all duration-300 rounded-md ",
                              !isMatched && "bg-gray-300",
                              isMatched && "hover:bg-primary hover:text-white",
                              selectedAttributes[currentVariation] ===
                                currentOption && "bg-primary text-white"
                            )}
                          >
                            {option}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex space-x-2 text-xl">
                <p className="font-medium line-through text-orange-300">
                  {formatPrice({
                    price: selectedVariation?.mrp || product?.mrp || 0,
                  })}
                </p>

                <p className="font-medium">
                  {formatPrice({
                    price: selectedVariation?.price || product?.basePrice || 0,
                  })}
                </p>
              </div>

              {/* MANAGE PRODUCT IN STOCK OR NOT */}
              <div className="flex space-x-2">
                {selectedVariation?.unlimited ||
                selectedVariation?.stock ? null : (
                  <p className="text-red-500">This frame is out of stock</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  disabled={
                    selectedVariation?.unlimited || selectedVariation?.stock
                      ? false
                      : true
                  }
                  className="w-full py-6 bg-primary hover:bg-primary/90"
                  onClick={openLensWizard}
                >
                  Select Lenses
                </Button>
                <div
                  className="aspect-square border h-12 rounded-md flex items-center justify-center cursor-pointer"
                  onClick={toggleFavorite}
                >
                  <Heart
                    fill={favorite ? "red" : "transparent"}
                    className={cn(favorite ? "text-red-400" : "text-gray-600")}
                  />
                </div>
              </div>

              {product && (
                <LensWizard
                  isOpen={isLensWizardOpen}
                  onClose={closeLensWizard}
                  product={product}
                  variation={selectedVariation!}
                  onAddToCart={handleAddToCartWithLenses}
                />
              )}
              <div>
                <p>{product?.description}</p>
              </div>
            </div>
          </div>
        </div>
        <div>
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger>Frame info</AccordionTrigger>
              <AccordionContent>
                Yes. It adheres to the WAI-ARIA design pattern.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-2">
              <AccordionTrigger>Description</AccordionTrigger>
              <AccordionContent>
                Yes. It comes with default styles that matches the other
                components&apos; aesthetic.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-3">
              <AccordionTrigger>Lens Recommendation</AccordionTrigger>
              <AccordionContent>
                Yes. It's animated by default, but you can disable it if you
                prefer.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
      {product && <RelatedProducts product={product} />}
    </Wrapper>
  );
};

export default page;
