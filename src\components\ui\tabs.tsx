"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface Tab {
  label: string;
  value: string;
  icon?: React.ReactNode;
}

interface TabsProps {
  tabs: Tab[];
  activeTab: string;
  onChange: (value: string) => void;
  tabClassName?: string;
  innerClassName?: string;
  activeClassName?: string;
  containerClassName?: string;
  children?: React.ReactNode;
}

export const Tabs: React.FC<TabsProps> = ({
  tabs,
  activeTab,
  onChange,
  tabClassName,
  innerClassName,
  activeClassName = "border-b-2 border-primary text-primary",
  containerClassName,
}) => {
  return (
    <div className={cn("w-full", containerClassName)}>
      <div className="flex border-b border-gray-200 space-x-4">
        {tabs.map((tab) => (
          <button
            key={tab.value}
            onClick={() => onChange(tab.value)}
            className={cn(
              "py-2 text-sm font-medium transition-colors",
              tab.value === activeTab
                ? activeClassName
                : "text-gray-500 hover:text-gray-700",
              tabClassName
            )}
          >
            <div className={cn("flex items-center gap-1", innerClassName)}>
              {tab.icon && <span>{tab.icon}</span>}
              {tab.label}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};
