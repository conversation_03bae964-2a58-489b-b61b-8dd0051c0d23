"use client";
import { api_interceptor } from "@/utils/api-interceptor";
import React, { useState } from "react";
import { toast } from "sonner";

export const ChatBubble = () => {
  const [info, setInfo] = useState({ name: "", email: "" });
  const [message, setMessage] = useState<string>("");
  const [chatbotVisible, setChatbotVisible] = useState<boolean>(false);
  const [fallback, setFallback] = useState<boolean>(true);

  const [chatbotMessage, setChatbotMessage] = useState<
    { person: string; message: string }[]
  >([
    {
      person: "AI",
      message: "Hello, how can I assist you today?",
    },
  ]);

  const onSendMessage = () => {
    const _message = message.trim();
    if (_message) {
      setChatbotMessage((prev) => [
        ...prev,
        { person: "You", message: _message },
      ]);
      setMessage("");

      if (fallback) {
        if (!info.name) {
          toast("Name is required");
          return;
        }

        if (!info.email) {
          toast("Email is required");
          return;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(info.email)) {
          toast("Invalid email");
          return;
        }

        api_interceptor
          .post("/chat/ticket", {
            message: _message,
            name: info.name,
            email: info.email,
          })
          .then((res) => {
            toast("Ticket has been created successfully");
          })
          .catch((e) => {
            toast.error("Something went wrong, please try again");
          });
      } else {
        api_interceptor
          .post("/chat", {
            message: _message,
          })
          .then((res) => {
            if (res.data.fallback) {
              setFallback(true);
              setChatbotMessage((prev) => [
                ...prev,
                {
                  person: "AI",
                  message:
                    "Sorry, I don't understand what you mean. please raise a ticket, we will get back to you.",
                },
              ]);
            } else {
              setChatbotMessage((prev) => [
                ...prev,
                { person: "AI", message: res.data.data.reply },
              ]);
            }
          });
      }
    }
  };

  return (
    <div>
      <div>
        <button
          onClick={() => setChatbotVisible(!chatbotVisible)}
          className="fixed bottom-4 right-4 inline-flex items-center justify-center text-sm font-medium disabled:pointer-events-none disabled:opacity-50 border rounded-full w-16 h-16 bg-black hover:bg-gray-700 m-0 cursor-pointer border-gray-200 bg-none p-0 normal-case leading-5 hover:text-gray-900"
          type="button"
          aria-haspopup="dialog"
          aria-expanded="false"
          data-state="closed"
        >
          <svg
            xmlns=" http://www.w3.org/2000/svg"
            width="30"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white block border-gray-200 align-middle"
          >
            <path
              d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"
              className="border-gray-200"
            ></path>
          </svg>
        </button>

        {chatbotVisible && (
          <div className="fixed flex flex-col bottom-[calc(4rem+1.5rem)] right-0 mr-4 bg-white p-6 rounded-lg border border-[#e5e7eb] w-[440px] h-[500px] shadow-[0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgb(0 0 0 / 0.05)]">
            <div className="flex flex-col space-y-1.5 pb-6">
              <h2 className="font-semibold text-lg tracking-tight">
                AI Chatbot
              </h2>
            </div>

            <div className="pr-4  min-w-[100%] flex-1 overflow-auto">
              {chatbotMessage.map((message) => (
                <div className="flex gap-3 my-4 text-gray-600 text-sm flex-1 items-start">
                  <div className="bg-gray-700 rounded-xl text-white px-3 py-2">
                    {message.person.slice(0, 2)}
                  </div>
                  <p className="leading-relaxed">
                    <span className="block font-bold text-gray-700">
                      {message.person}
                    </span>
                    {message.message}
                  </p>
                </div>
              ))}
            </div>
            <div className="flex items-center pt-0">
              <form className="w-full space-y-2">
                {fallback && (
                  <div className="flex gap-2">
                    <input
                      className="flex h-10 w-full rounded-md border border-[#e5e7eb] px-3 py-2 text-sm placeholder-[#6b7280] focus:outline-none focus:ring-2 focus:ring-[#9ca3af] disabled:cursor-not-allowed disabled:opacity-50 text-[#030712] focus-visible:ring-offset-2"
                      placeholder="Name..."
                      value={info.name}
                      onChange={(e) =>
                        setInfo((prev) => ({ ...prev, name: e.target.value }))
                      }
                    />
                    <input
                      className="flex h-10 w-full rounded-md border border-[#e5e7eb] px-3 py-2 text-sm placeholder-[#6b7280] focus:outline-none focus:ring-2 focus:ring-[#9ca3af] disabled:cursor-not-allowed disabled:opacity-50 text-[#030712] focus-visible:ring-offset-2"
                      placeholder="Email..."
                      value={info.email}
                      onChange={(e) =>
                        setInfo((prev) => ({ ...prev, email: e.target.value }))
                      }
                    />
                  </div>
                )}
                <div className="flex items-center justify-center w-full space-x-2">
                  <input
                    className="flex h-10 w-full rounded-md border border-[#e5e7eb] px-3 py-2 text-sm placeholder-[#6b7280] focus:outline-none focus:ring-2 focus:ring-[#9ca3af] disabled:cursor-not-allowed disabled:opacity-50 text-[#030712] focus-visible:ring-offset-2"
                    placeholder="Type your message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                  />
                  <button
                    onClick={onSendMessage}
                    className="inline-flex items-center justify-center rounded-md text-sm font-medium text-[#f9fafb] disabled:pointer-events-none disabled:opacity-50 bg-black hover:bg-[#111827E6] h-10 px-4 py-2"
                  >
                    Send
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
