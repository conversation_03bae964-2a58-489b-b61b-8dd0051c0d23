"use client";

import { useToggleSection } from "@/utils/use-toggle-section";
import { ChevronDown, ChevronUp } from "lucide-react";
import { But<PERSON> } from "./button";

export const ToggleBlock = ({
  title,
  children,
  defaultValue = true,
}: {
  title: string;
  children: React.ReactNode;
  defaultValue?: boolean;
}) => {
  const { isVisible, toggle } = useToggleSection(defaultValue);

  return (
    <div>
      <Button
        onClick={toggle}
        className="shadow-none text-inherit hover:text-inherit hover:bg-transparent flex items-center justify-between w-full text-left text-sm font-medium bg-gray-100 px-0 py-7 rounded border-b border-gray-200"
        size={"lg"}
      >
        {title}
        {isVisible ? (
          <ChevronUp className="w-4 h-4 transform transition-transform duration-300" />
        ) : (
          <ChevronDown className="w-4 h-4 transform transition-transform duration-300" />
        )}
      </Button>

      <div
        className={`transition-all duration-300 overflow-hidden ${
          isVisible ? "max-h-96 opacity-100 mt-3" : "max-h-0 opacity-0 p-0"
        }`}
      >
        <div className="text-sm text-gray-700">{children}</div>
      </div>
    </div>
  );
};
