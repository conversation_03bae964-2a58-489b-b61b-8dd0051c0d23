"use client";

import { Wrapper } from "@/components/common/wrapper";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import store, { RootState, useAppDispatch } from "@/store/store";
import { CheckoutSteps } from "@/components/checkout/checkout-steps";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { setCurrentStep, resetCheckout } from "@/store/slices/checkout-slice";
import { CheckCircle, Home, Package, Printer, Tag } from "lucide-react";
import { formatPrice } from "@/utils/format-price";
import { calculateTaxes, getSubtotal, getTotal } from "@/utils/cart-price";
import { createOrder } from "@/store/slices/order-slice";
import { toast } from "sonner";
import { ICartItem } from "@/interfaces/cart";


const ConfirmationPage = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const {
    cart,
    tax: { taxes },
  } = useSelector((state: RootState) => state);
  const { shippingAddress, billingAddress, paymentDetails, orderNotes } =
    useSelector((state: RootState) => state.checkout);
  const [order, setOrder] = useState<any>({});

  // State to store the order ID
  const [orderId, setOrderId] = useState("");

  useEffect(() => {
    dispatch(setCurrentStep(4));

    // Redirect if essential data is missing
    if (!shippingAddress || !billingAddress || !paymentDetails) {
      router.push("/checkout");
      return;
    }

    // Create the order data
    const orderData = {
      paymentMethod: paymentDetails.method,
      shippingMethod: "standard",
      shippingAddress,
      billingAddress,
      shippingCost: 0,
      note: orderNotes || "",
      subtotal: getSubtotal(cart.cart),
      tax: calculateTaxes(taxes, cart.cart),
      taxes: taxes,
      items: cart.cart.map((item) => ({
        product: {
          name: item.product.name,
          thumbnail: item.product.thumbnail,
          slug: item.product.slug,
          price: item.variation?.price || item.product.basePrice,
          mrp: item.variation?.mrp || item.product.mrp,
          _id: item.product._id,
        },
        lensData: item.lensData,
        variation: {
          _id: item.variation?._id,
          attribute: Object.keys(item.variation?.attributes || {}),
          thumbnail: item.variation?.thumbnail,
        },
        quantity: item.quantity,
        price: item.variation?.price || item.product.basePrice,
        total:
          (item.variation?.price || item.product.basePrice) * item.quantity,
      })),
      totalAmount: +calculateTaxes(taxes, cart.cart) + +getTotal(cart.cart),
      payableAmount:
        +calculateTaxes(taxes, cart.cart) +
        +getTotal(cart.cart, {
          discount: cart.automatic_discount?.amount,
          couponDiscount: cart.couponDiscount || 0,
          shippingCost: 0,
        }),
      coupon: cart.coupon,
      discount: cart.couponDiscount,
    };
    
    store
      .dispatch(createOrder(orderData))
      .unwrap()
      .then((res) => {
        setOrder(res.data);
        setOrderId(res.data.orderId!);
        toast.success(res.message);
      })
      .catch((err) => {
        toast.error("Failed to place order. Please try again.");
      });
  }, [
    shippingAddress,
    billingAddress,
    paymentDetails,
    router,
    cart.cart,
    orderNotes,
  ]);

  const handleContinueShopping = () => {
    dispatch(resetCheckout());
    router.push("/");
  };

  const handleViewOrders = () => {
    dispatch(resetCheckout());
    router.push("/account/orders");
  };

  return (
    <Wrapper vertical>
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>

      <CheckoutSteps currentStep={4} />

      <div className="max-w-3xl mx-auto">
        <div className="border border-gray-200 rounded-md p-8 text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>

          <h2 className="text-2xl font-bold mb-2">Thank You for Your Order!</h2>
          <p className="text-gray-600 mb-4">
            Your order has been placed successfully. We&apos;ve sent you an
            email with all the details.
          </p>

          <div className="bg-gray-50 p-4 rounded-md inline-block mb-4">
            <p className="text-gray-700">
              Order ID: <span className="font-semibold">#{orderId}</span>
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Order Summary */}
          <div className="border border-gray-200 rounded-md p-6">
            <h3 className="font-semibold text-lg mb-4">Order Summary</h3>

            <div className="space-y-3 mb-4">
              {cart.cart.map((item: ICartItem) => (
                <div key={item.id} className="flex justify-between text-sm">
                  <span>
                    {item.product.name} x {item.quantity}
                  </span>
                  <span className="font-medium">
                    {formatPrice({
                      price:
                        (item.variation?.price || item.product.basePrice) *
                        item.quantity,
                    })}
                  </span>
                </div>
              ))}
            </div>

            <div className="border-t border-gray-200 pt-3 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal</span>
                <span>{formatPrice({ price: order.subtotal! })}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Taxes</span>
                <span>
                  {formatPrice({ price: +calculateTaxes(taxes, cart.cart) })}
                </span>
              </div>

              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>
                  {formatPrice({
                    price: order.totalAmount,
                  })}
                </span>
              </div>

              {cart.coupon && (
                <div className="flex justify-between text-sm text-green-600">
                  <span className="flex items-center">
                    <Tag className="w-3 h-3 mr-1" />
                    Coupon ({cart.coupon.code})
                  </span>
                  <span>-{formatPrice({ price: cart.couponDiscount })}</span>
                </div>
              )}

              {cart.coupon && (
                <div className="flex justify-between font-semibold">
                  <span>Payable amount</span>
                  <span>
                    {formatPrice({
                      price: order.payableAmount,
                    })}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Shipping Information */}
          <div className="border border-gray-200 rounded-md p-6">
            <h3 className="font-semibold text-lg mb-4">Shipping Information</h3>

            <div className="space-y-4">
              <div>
                <h4 className="text-sm text-gray-500 mb-1">Shipping Address</h4>
                <div className="text-sm">
                  <p className="font-medium">{shippingAddress?.fullName}</p>
                  <p>{shippingAddress?.address}</p>
                  <p>
                    {shippingAddress?.city}, {shippingAddress?.state}{" "}
                    {shippingAddress?.postalCode}
                  </p>
                  <p>{shippingAddress?.country}</p>
                  <p>Phone: {shippingAddress?.phone}</p>
                </div>
              </div>

              <div>
                <h4 className="text-sm text-gray-500 mb-1">Payment Method</h4>
                <p className="text-sm capitalize">
                  {paymentDetails?.method.replace(/_/g, " ")}
                  {paymentDetails?.method === "CreditCard" &&
                    paymentDetails.cardNumber && (
                      <span>
                        {" "}
                        ending in {paymentDetails.cardNumber.slice(-4)}
                      </span>
                    )}
                </p>
              </div>

              {orderNotes && (
                <div>
                  <h4 className="text-sm text-gray-500 mb-1">Order Notes</h4>
                  <p className="text-sm">{orderNotes}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="outline"
            className="flex items-center"
            onClick={handleContinueShopping}
          >
            <Home className="mr-2 h-4 w-4" />
            Continue Shopping
          </Button>

          <Button className="flex items-center" onClick={handleViewOrders}>
            <Package className="mr-2 h-4 w-4" />
            View My Orders
          </Button>

          <Button
            variant="outline"
            className="flex items-center"
            onClick={() => window.print()}
          >
            <Printer className="mr-2 h-4 w-4" />
            Print Receipt
          </Button>
        </div>
      </div>
    </Wrapper>
  );
};

export default ConfirmationPage;
