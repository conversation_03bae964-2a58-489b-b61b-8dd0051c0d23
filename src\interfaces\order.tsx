import { IAddress } from "./address";

export interface IOrderProductSchema {
  name: string;
  thumbnail: string;
  slug: string;
  price: number;
  mrp: number;
  _id: string;
}

export interface IOrderVariationSchema {
  _id: string;
  thumbnail: string;
  attribute: string[];
}

export interface IOrderTaxSchema {
  type: string;
  amount: number;
  isInclusive: boolean;
  name: boolean;
  value: number;
  _id: string;
}

export interface IOrderItem {
  _id?: string;
  amount?: number;
  quantity: number;
  product: IOrderProductSchema;
  tax: IOrderTaxSchema;
  variation?: IOrderVariationSchema;
}

export interface IOrderCouponSchema {
  code: string;
  discountType: number;
  discountValue: number;
  applicableTo: {
    products: string[];
    categories: string[];
  };
}

export interface IOrder {
  _id?: string;
  note?: string;
  orderId?: string;
  subtotal: number;
  shippingCost: number;
  tax?: number;
  taxes: IOrderTaxSchema[];
  discount?: number;
  totalAmount: number;
  items: IOrderItem[];
  shippingAddress: IAddress;
  billingAddress: IAddress;
  shippingMethod?: string;
  paymentMethod?: string;
  status: string;
  trackingNumber?: string;
  estimatedDelivery?: string;
  delivery?: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  coupon: IOrderCouponSchema;
}

export interface ICreateOrder {
  totalAmount: number;
  shippingAddress: IAddress;
  billingAddress: IAddress;
  coupon: string;
  discount: number;
  taxes: { name: string; value: number }[];
  note: string;
  items: {
    product: string;
    variation?: string | null;
    quantity: number;
    price: number;
    total: number;
  }[];
}
