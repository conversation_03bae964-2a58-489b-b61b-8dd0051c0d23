import { ICartItem, ITax } from "@/interfaces";

type PricingOptions = {
  discount?: number; // e.g., 10 (flat discount)
  tax?: number; // e.g., 5.5 (percentage or flat)
  taxType?: "percentage" | "flat";
  shippingCost?: number; // e.g., 50
  couponDiscount?: number; // e.g., 25
};

export const getSubtotal = (cart: ICartItem[]): number => {
  return cart.reduce((sum, item) => {
    const basePrice = item.variation?.price || item.product.basePrice;
    const lensPrice = item.lensData?.lensPackage?.price || 0;
    return sum + (basePrice + lensPrice) * item.quantity;
  }, 0);
};

export const getTotal = (
  cart: ICartItem[],
  options: PricingOptions = {}
): number => {
  const subtotal = getSubtotal(cart);

  const { discount = 0, shippingCost = 0, couponDiscount = 0 } = options;

  const total = subtotal - discount + shippingCost - couponDiscount;

  return Math.max(0, total); // Avoid negative totals
};

export const calculateTax = (tax: ITax, cart: ICartItem[]) => {
  cart;

  if (tax.type.toLowerCase() === "percentage") {
    return (getSubtotal(cart) * tax.value) / 100;
  } else {
    return tax.value * cart.reduce((prev, cur) => prev + cur.quantity, 0);
  }
};

export const calculateTaxes = (taxes: ITax[], cart: ICartItem[]) => {
  const totalTax = taxes.reduce(
    (acc, cur) => acc + (cur.isInclusive ? 0 : calculateTax(cur, cart)),
    0
  );
  return totalTax.toFixed(2);
};
