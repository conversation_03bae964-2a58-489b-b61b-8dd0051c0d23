import { <PERSON><PERSON> } from "@/components/ui/button";
import { EMAIL, PHONE } from "@/config/app-config";
import {
  ArrowRight,
  CornerDownLeft,
  CreditCard,
  FileLock2,
  Layers,
  LockKeyholeOpen,
  MessageCircleMore,
  NotepadText,
  PhoneCall,
  Search,
  Store,
  Truck,
  UserRound,
} from "lucide-react";
import Link from "next/link";
import React from "react";

const Page = () => {
  const navigation = [
    {
      icon: Truck,
      name: "Track Order",
      href: "/track-order",
    },
    {
      icon: LockKeyholeOpen,
      name: "Reset Password",
      href: "/reset-password",
    },
    {
      icon: CreditCard,
      name: "Payment Option",
      href: "/payment-option",
    },
    {
      icon: UserRound,
      name: "User & Account",
      href: "/profile",
    },
    {
      icon: Layers,
      name: "Wishlist & Compare",
      href: "/account/wishlist",
    },
    {
      icon: NotepadText,
      name: "Shipping & Billing",
      href: "/address",
    },
    {
      icon: FileLock2,
      name: "Privacy Policy",
      href: "/privacy-policy",
    },
  ];

  const topics = [
    {
      name: "How do I return my item?",
      href: "/faq?q=query",
    },
    {
      name: "What is the return policy?",
      href: "/faq?q=query",
    },
    {
      name: "How long is the refund process?",
      href: "/faq?q=query",
    },
    {
      name: "What are the 'Delivery Timelines'?",
      href: "/faq?q=query",
    },
    {
      name: "What is 'Discover your Daraz Campaign 2022'?",
      href: "/faq?q=query",
    },
    {
      name: "What is the Voucher & Gift Offer in this Campaign?",
      href: "/faq?q=query",
    },
    {
      name: "How to cancel Order?",
      href: "/faq?q=query",
    },
    {
      name: "Ask the Digital and Device Community",
      href: "/faq?q=query",
    },
    {
      name: "How to change my shop name?",
      href: "/faq?q=query",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="px-16 grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-14 gap-y-10 py-16 bg-[url('/help.png')] bg-no-repeat bg-cover">
        <div className="w-11/12">
          <p className="bg-primary w-max px-3 py-2 text-white font-semibold mt-8 mb-4">
            Help Center
          </p>

          <h2 className="text-4xl font-semibold mb-8">How we can help you?</h2>

          <div className="flex items-center border p-3 rounded-lg w-auto">
            {/* Search Icon on the Left */}
            <Search size={22} className="mr-3" />

            {/* Input Field */}
            <input
              type="text"
              placeholder="Enter your question or keyword"
              className="flex-grow border-none outline-none w-auto"
            />

            {/* Search Button on the Right */}
            <Button className="ml-2 bg-primary text-white hidden sm:block">
              Search
            </Button>

            <Button className="ml-2 bg-primary text-white block sm:hidden">
              <CornerDownLeft />
            </Button>
          </div>
        </div>

        <div className="h-4/5 mx-auto">
          {/* <img src="/best-seller.webp" alt="" className="w-100" /> */}
        </div>
      </div>

      {/* HELP LINKS SECTION */}
      <div className="py-12 qfs-core-team border-gray-200 border-t">
        <h2 className="font-semibold text-center text-4xl mb-12">
          What can we assist you with today?
        </h2>

        {/* HELP LINKS */}
        <div className="mx-16 px-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4  gap-x-10 gap-y-6">
          {navigation.map((action, index) => {
            return (
              <Link
                href={action.href}
                key={"team" + index}
                className="w-100 flex flex-col lg:flex-row align-middle border px-4 py-6 border-accent hover:border-primary items-center cursor-pointer"
              >
                <action.icon className="mr-4 text-primary" size={28} />

                <h1 className="font-semibold">{action.name}</h1>
              </Link>
            );
          })}
        </div>
      </div>

      {/* POPULAR TOPICS SECTION */}
      <div className="py-12 qfs-core-team border-gray-200 border-t">
        <h2 className="font-semibold text-center text-4xl mb-12">
          Popular Topics
        </h2>

        {/* POPULAR TOPICS */}
        <ul className=" list-disc mx-16 px-auto grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 gap-x-10 gap-y-6">
          {topics.map((action, index) => {
            return (
              <Link
                href={action.href}
                key={"team" + index}
                className="w-100 hover:text-primary items-center cursor-pointer"
              >
                <li className="font-semibold">{action.name}</li>
              </Link>
            );
          })}
        </ul>
      </div>

      {/* CONTACT US */}
      <div className="py-12 qfs-core-team bg-slate-100">
        <div className="text-center w-100">
          <Button className="transition-all duration-300 bg-blue-400 text-white rounded-none hover:text-white hover:bg-blue-500 text-sm mb-6">
            <Link href={"/contact-us"}>Contact Us</Link>
          </Button>

          <h2 className="font-semibold text-center text-4xl mb-12">
            Didn't find your answers?
          </h2>
        </div>

        {/* CONTACT */}
        <div className="px-auto grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-2 gap-x-10 gap-y-6 w-3/4 mx-auto">
          {/* CALL */}
          <div className="w-100 flex flex-col md:flex-row px-4 py-6 items-start bg-white">
            <div className="bg-blue-50 flex items-center size-24 align-middle text-center justify-center mr-6 sm:mb-3">
              <PhoneCall className="text-blue-400" size={40} />
            </div>

            <div className="qfs-info-container">
              <h1 className="font-semibold mb-2 text-lg">Call us now</h1>

              <p className="text-sm mb-4 text-gray-500">
                We are available online from 09:00 AM to 05:00 PM (GMT +05:30).
              </p>

              <h1 className="font-semibold text-2xl mb-4">{PHONE}</h1>

              <Button className="bg-blue-400 hover:bg-blue-500 w-auto h-auto">
                <Link
                  href={`tel:${PHONE}`}
                  className="w-100 h-100 flex align-middle items-center text-lg px-2 py-1"
                >
                  Call Now
                  <ArrowRight className="ml-2" size={30} />
                </Link>
              </Button>
            </div>
          </div>

          {/* CHAT */}
          <div className="w-100 flex flex-col md:flex-row px-4 py-6 items-start bg-white">
            <div className="bg-green-50 flex items-center size-24 align-middle text-center justify-center mr-6 sm:mb-3">
              <MessageCircleMore className="text-green-500" size={40} />
            </div>

            <div className="qfs-info-container">
              <h1 className="font-semibold mb-2 text-lg">Call us now</h1>

              <p className="text-sm mb-4 text-gray-500">
                We are available online from 09:00 AM to 05:00 PM (GMT +05:30).
              </p>

              <h1 className="font-semibold text-2xl mb-4">{EMAIL}</h1>

              <Button className="bg-green-500 hover:bg-green-600 w-auto h-auto">
                <Link
                  href={`mailto:${EMAIL}`}
                  className="w-100 h-100 flex align-middle items-center text-lg px-2 py-1"
                >
                  Contact Us
                  <ArrowRight className="ml-2" size={30} />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
