"use client";
import React, { useState } from "react";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
} from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import {
  ChevronDownIcon,
  FunnelIcon,
  ChevronUpIcon,
} from "@heroicons/react/20/solid";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Product } from "@/components/common/product";
import Image from "next/image";
import { IFilter, IBreadcrumb } from "@/interfaces";
import { TopBreadcrumb } from "@/components/common/top-breadcrumb";

const sortOptions = [
  { name: "Most Popular", href: "#", current: true },
  { name: "Best Rating", href: "#", current: false },
  { name: "Newest", href: "#", current: false },
  { name: "Price: Low to High", href: "#", current: false },
  { name: "Price: High to Low", href: "#", current: false },
];

const filters: IFilter[] = [
  {
    id: "filter",
    name: "Filter",
    options: [
      { value: "sale", label: "Sale", checked: true },
      { value: "rush-delivery", label: "Rush Delivery", checked: false },
    ],
  },
  {
    id: "gender-age",
    name: "Gender & Age",
    options: [
      { value: "women", label: "Women", checked: true },
      { value: "men", label: "Men", checked: false },
      { value: "kids", label: "Kids", checked: false },
    ],
  },
  {
    id: "shape",
    name: "Shape",
    options: [
      {
        value: "square",
        label: "Square",
        checked: true,
        image: "/frame-shape/1.svg",
      },
      {
        value: "cat-eye",
        label: "Cat-Eye",
        checked: true,
        image: "/frame-shape/2.svg",
      },
      {
        value: "round",
        label: "Round",
        checked: false,
        image: "/frame-shape/3.svg",
      },
      {
        value: "rectangle",
        label: "Rectangle",
        checked: false,
        image: "/frame-shape/4.svg",
      },
      {
        value: "aviator",
        label: "Aviator",
        checked: false,
        image: "/frame-shape/5.svg",
      },
      {
        value: "browline",
        label: "Browline",
        checked: false,
        image: "/frame-shape/6.svg",
      },
      {
        value: "geometric",
        label: "Geometric",
        checked: false,
        image: "/frame-shape/1.svg",
      },
      {
        value: "oval",
        label: "Oval",
        checked: false,
        image: "/frame-shape/2.svg",
      },
      {
        value: "heart",
        label: "Heart",
        checked: false,
        image: "/frame-shape/3.svg",
      },
      {
        value: "wrap-around",
        label: "Wrap-Around",
        checked: false,
        image: "/frame-shape/4.svg",
      },
      {
        value: "full-rim",
        label: "Full-Rim",
        checked: false,
        image: "/frame-shape/5.svg",
      },
      {
        value: "half-rim",
        label: "Half-Rim",
        checked: false,
        image: "/frame-shape/6.svg",
      },
      {
        value: "rimless",
        label: "Rimless",
        checked: false,
        image: "/frame-shape/1.svg",
      },
    ],
  },
  {
    id: "frame-sizes",
    name: "Frame Sizes",
    options: [
      { value: "xs", label: "XS (110-118mm)", checked: false },
      { value: "s", label: "S (119-125mm)", checked: true },
      { value: "m", label: "M (126-132mm)", checked: false },
      { value: "l", label: "L (133-140mm)", checked: false },
      { value: "xl", label: "XL (141+ mm)", checked: false },
    ],
  },
  {
    id: "color",
    name: "Color",
    options: [
      { value: "black", label: "Black", checked: true },
      { value: "brown", label: "Brown", checked: false },
      { value: "blue", label: "Blue", checked: false },
      { value: "green", label: "Green", checked: false },
      { value: "red", label: "Red", checked: false },
      { value: "purple", label: "Purple", checked: false },
      { value: "yellow", label: "Yellow", checked: false },
    ],
  },
  {
    id: "lens",
    name: "Lens Type",
    options: [
      { value: "polarized", label: "Polarized", checked: false },
      { value: "mirror", label: "Mirror", checked: false },
      { value: "gradient", label: "Gradient", checked: true },
      { value: "clear", label: "Clear", checked: false },
      { value: "blue-light", label: "Blue Light Blocking", checked: false },
    ],
  },
];

const page = () => {
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const breadCrumbs: IBreadcrumb[] = [
    { label: "Home", href: "/" },
    { label: "Man", href: "" },
  ];

  return (
    <div className="bg-white">
      <div>
        {/* Mobile filter dialog */}
        <Dialog
          open={mobileFiltersOpen}
          onClose={setMobileFiltersOpen}
          className="relative z-40 lg:hidden"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-black/25 transition-opacity duration-300 ease-linear data-closed:opacity-0"
          />

          <div className="fixed inset-0 z-40 flex">
            <DialogPanel
              transition
              className="relative ml-auto flex size-full max-w-xs transform flex-col overflow-y-auto bg-white py-4 pb-12 shadow-xl transition duration-300 ease-in-out data-closed:translate-x-full"
            >
              <div className="flex items-center justify-between px-4">
                <h2 className="text-lg font-medium text-gray-900">Filters</h2>
                <button
                  type="button"
                  onClick={() => setMobileFiltersOpen(false)}
                  className="-mr-2 flex size-10 items-center justify-center rounded-md bg-white p-2 text-gray-400"
                >
                  <span className="sr-only">Close menu</span>
                  <XMarkIcon aria-hidden="true" className="size-6" />
                </button>
              </div>

              {/* Filters */}
              <form className="mt-4 border-t border-gray-200">
                <h3 className="sr-only">Categories</h3>

                {filters.map((section) => (
                  <Disclosure
                    key={section.id}
                    as="div"
                    className="border-t border-gray-200 px-4 py-6"
                  >
                    {({ open }) => (
                      <>
                        {" "}
                        <h3 className="-mx-2 -my-3 flow-root">
                          <DisclosureButton className="group flex w-full items-center justify-between bg-white px-2 py-3 text-gray-400 hover:text-gray-500">
                            <span className="font-medium text-gray-900">
                              {section.name}
                            </span>
                            <span className="ml-6 flex items-center">
                              <ChevronDownIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "hidden" : "block"
                                }`}
                              />
                              <ChevronUpIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "block" : "hidden"
                                }`}
                              />
                            </span>
                          </DisclosureButton>
                        </h3>
                        <DisclosurePanel className="pt-6">
                          <div className="space-y-6">
                            {section.options.map((option, optionIdx) => (
                              <div
                                key={option.value}
                                className="flex items-center gap-3"
                              >
                                <div className="flex h-5 shrink-0 items-center">
                                  <div className="group grid size-4 grid-cols-1">
                                    <Checkbox
                                      defaultValue={option.value}
                                      id={`filter-${section.id}-${optionIdx}`}
                                      name={`${section.id}[]`}
                                    />
                                  </div>
                                </div>
                                {option?.image && (
                                  <Image
                                    src={option.image}
                                    alt="Filter image"
                                    width={200}
                                    height={200}
                                    className="h-10 w-10 object-contain"
                                  />
                                )}
                                <label
                                  htmlFor={`filter-${section.id}-${optionIdx}`}
                                  className="min-w-0 flex-1 text-gray-500"
                                >
                                  {option.label}
                                </label>
                              </div>
                            ))}
                          </div>
                        </DisclosurePanel>
                      </>
                    )}
                  </Disclosure>
                ))}
              </form>
            </DialogPanel>
          </div>
        </Dialog>

        <div className="mx-auto px-4 sm:px-6 lg:px-8">
          <section aria-labelledby="products-heading" className="pt-6 pb-24">
            <TopBreadcrumb breadcrumbs={breadCrumbs} />

            <div className="grid grid-cols-1 gap-x-8 gap-y-10 lg:grid-cols-4">
              {/* Filters */}
              <form className="hidden lg:block">
                {filters.map((section) => (
                  <Disclosure
                    key={section.id}
                    as="div"
                    className="border-b border-gray-200 py-6"
                  >
                    {({ open }) => (
                      <>
                        {" "}
                        <h3 className="-my-3 flow-root">
                          <DisclosureButton className="group flex w-full items-center justify-between bg-white py-3 text-sm text-gray-400 hover:text-gray-500">
                            <span className="font-medium text-gray-900">
                              {section.name}
                            </span>
                            <span className="ml-6 flex items-center">
                              <ChevronDownIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "hidden" : "block"
                                }`}
                              />
                              <ChevronUpIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "block" : "hidden"
                                }`}
                              />
                            </span>
                          </DisclosureButton>
                        </h3>
                        <DisclosurePanel className="pt-6">
                          <div className="space-y-4">
                            {section.options.map((option, optionIdx) => (
                              <div
                                key={option.value}
                                className="flex items-center gap-3"
                              >
                                <div className="flex h-5 shrink-0 items-center">
                                  <div className="group grid size-4 grid-cols-1">
                                    <Checkbox
                                      defaultValue={option.value}
                                      id={`filter-${section.id}-${optionIdx}`}
                                      name={`${section.id}[]`}
                                    />
                                  </div>
                                </div>

                                {option?.image && (
                                  <Image
                                    src={option.image}
                                    alt="Filter image"
                                    width={200}
                                    height={200}
                                    className="h-10 w-10"
                                  />
                                )}
                                <label
                                  htmlFor={`filter-${section.id}-${optionIdx}`}
                                  className="min-w-0 flex-1 text-gray-500"
                                >
                                  {option.label}
                                </label>
                              </div>
                            ))}
                          </div>
                        </DisclosurePanel>
                      </>
                    )}
                  </Disclosure>
                ))}
              </form>

              {/* Product grid */}
              <div className="lg:col-span-3">
                <div>
                  <Image
                    src={"/category/banner/1.svg"}
                    alt="Banner"
                    width={1000}
                    height={1000}
                    className="w-full rounded-lg"
                  />
                </div>
                <div className="flex items-center justify-between border-b border-gray-200 py-6 mb-6">
                  <h1 className="text-2xl font-semibold tracking-tight text-gray-900">
                    Sunglasses
                  </h1>

                  <div className="flex items-center gap-4">
                    <p>Showing 1-12 of 50 results</p>
                    <Menu as="div" className="relative inline-block text-left">
                      <div>
                        <MenuButton className="group inline-flex justify-center text-sm font-medium text-gray-700 hover:text-gray-900">
                          Sort
                          <ChevronDownIcon
                            aria-hidden="true"
                            className="-mr-1 ml-1 size-5 shrink-0 text-gray-400 group-hover:text-gray-500"
                          />
                        </MenuButton>
                      </div>

                      <MenuItems
                        transition
                        className="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white ring-1 shadow-2xl ring-black/5 transition focus:outline-hidden data-closed:scale-95 data-closed:transform data-closed:opacity-0 data-enter:duration-100 data-enter:ease-out data-leave:duration-75 data-leave:ease-in"
                      >
                        <div className="py-1">
                          {sortOptions.map((option) => (
                            <MenuItem key={option.name}>
                              <a
                                href={option.href}
                                className={cn(
                                  option.current
                                    ? "font-medium text-gray-900"
                                    : "text-gray-500",
                                  "block px-4 py-2 text-sm data-focus:bg-gray-100 data-focus:outline-hidden"
                                )}
                              >
                                {option.name}
                              </a>
                            </MenuItem>
                          ))}
                        </div>
                      </MenuItems>
                    </Menu>
                    <button
                      type="button"
                      onClick={() => setMobileFiltersOpen(true)}
                      className="-m-2 p-2 text-gray-400 hover:text-gray-500 sm:ml-6 lg:hidden"
                    >
                      <span className="sr-only">Filters</span>
                      <FunnelIcon aria-hidden="true" className="size-5" />
                    </button>
                  </div>
                </div>

                <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3">
                  {Array(10)
                    .fill(0)
                    .map((_, index) => (
                      <Product key={index} />
                    ))}
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default page;
