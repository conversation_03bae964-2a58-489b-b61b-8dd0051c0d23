"use client";
import React, { useState } from "react";
import {
  ArrowLeft,
  ArrowRight,
  Package,
  RefreshCw,
  HelpCircle,
  Info,
  AlertTriangle,
  ShieldCheck,
} from "lucide-react";
import { Wrapper } from "@/components/common/wrapper";

const ReturnPolicies = () => {
  const [activeTab, setActiveTab] = useState("glasses");
  const [activeSection, setActiveSection] = useState(null);

  const toggleSection = (section: any) => {
    setActiveSection(activeSection === section ? null : section);
  };

  const renderGlassesPolicy = () => {
    const sections = [
      {
        id: "basic-policy",
        title: "Basic Return Policy",
        icon: <Package size={20} />,
        content: (
          <div>
            <p className="mb-4">
              At our company, we want you to be completely satisfied with your
              purchase. If you are unhappy with your glasses for any reason, you
              can return, exchange, or receive a refund as long as you meet the
              following conditions:
            </p>
            <ul className="list-disc pl-6 space-y-2 mb-4">
              <li>
                <strong>Return Period:</strong> Glasses must be returned in the
                same condition you received them within <strong>30 days</strong>{" "}
                from the date of dispatch.
              </li>
              <li>
                <strong>Condition of Return:</strong> Items must be returned in
                original condition and include all accessories, such as cases
                and cleaning cloths. Charges may apply for any accessories that
                are not returned.
              </li>
            </ul>
            <p>
              For your return, we recommend obtaining{" "}
              <strong>proof of postage</strong>. You can obtain this from your
              local Royal Mail outlet or it will be provided if you opt for
              collection.
            </p>
          </div>
        ),
      },
      {
        id: "return-process",
        title: "Return Process",
        icon: <RefreshCw size={20} />,
        content: (
          <div>
            <p className="mb-4">
              For returning your glasses, please follow these steps:
            </p>
            <ol className="list-decimal pl-6 space-y-3 mb-4">
              <li>
                <strong>Initiate Return:</strong> Log in to 'My Orders', locate
                the item you wish to return, and click the 'Return' button.
                Follow the instructions to confirm your return method.
              </li>
              <li>
                <strong>Returns Label:</strong> After your request, you'll
                receive an email with a barcode that you can use to print a{" "}
                <strong>free return label</strong>.
              </li>
              <li>
                <strong>Packaging:</strong> Re-seal your return in the original
                packaging, affix the provided return label, and drop it off at
                the local Royal Mail outlet.
              </li>
            </ol>
          </div>
        ),
      },
      {
        id: "faulty-exchanges",
        title: "Faulty Items & Exchanges",
        icon: <AlertTriangle size={20} />,
        content: (
          <div>
            <p className="mb-4">
              If you receive faulty glasses, please reach out through our
              contact page. We will gladly assist you. Exchanged items will
              include both frames and lenses, even if only one part is
              defective. If the exchanged glasses still don't meet your
              expectations, we will guide you through the refund process.
            </p>
            <div className="bg-primary/10 p-4 rounded-lg mt-4">
              <h4 className="font-semibold mb-2">Contact Us</h4>
              <p>
                If you didn't receive your prepaid return label after submitting
                a request, you can also reach out via email or phone. Find our
                contact details on our contact page.
              </p>
            </div>
          </div>
        ),
      },
      {
        id: "additional-info",
        title: "Additional Return Information",
        icon: <Info size={20} />,
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-3">
              <li>
                <strong>Return Timeframe:</strong> You have{" "}
                <strong>30 days</strong> from the dispatch date to return your
                glasses.
              </li>
              <li>
                <strong>Items Needed for Returns:</strong> To return your
                glasses, include the original order contents, your order number,
                and one of our free return labels. Please do not send personal
                items like your prescription. Contact us via email at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary/60 hover:underline"
                >
                  <EMAIL>
                </a>{" "}
                if you need to send a copy of your prescription.
              </li>
              <li>
                <strong>Confirmation of Receipt:</strong> We will notify you via
                email once we receive your return. Processing may take a few
                days; we aim to complete all returns within{" "}
                <strong>7 working days</strong> from receipt.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "common-questions",
        title: "Common Questions",
        icon: <HelpCircle size={20} />,
        content: (
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold">
                Can I return my glasses if I don't like them?
              </h4>
              <p>Yes, you can return them for any reason.</p>
            </div>
            <div>
              <h4 className="font-semibold">
                What if a screw has come loose from my frame?
              </h4>
              <p>
                Visit "My Orders" and select the "Fix and Replace" option to
                arrange a return. Alternatively, contact
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary/60 hover:underline ml-1"
                >
                  <EMAIL>
                </a>{" "}
                for assistance.
              </p>
            </div>
            <div>
              <h4 className="font-semibold">
                I lost my return label; how do I get a new one?
              </h4>
              <p>
                If you haven't received your return label after the request,
                please contact us, and we will arrange a replacement.
              </p>
            </div>
          </div>
        ),
      },
    ];

    return (
      <div className="space-y-4">
        {sections.map((section) => (
          <div key={section.id} className="border rounded-lg overflow-hidden">
            <button
              onClick={() => toggleSection(section.id)}
              className="w-full px-4 py-3 text-left font-semibold flex justify-between items-center hover:bg-gray-50"
            >
              <div className="flex items-center">
                <span className="text-primary/60 mr-3">{section.icon}</span>
                {section.title}
              </div>
              <span className="text-xl">
                {activeSection === section.id ? "−" : "+"}
              </span>
            </button>
            {activeSection === section.id && (
              <div className="p-4 bg-white border-t">{section.content}</div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderReglazingPolicy = () => {
    const sections = [
      {
        id: "agreement",
        title: "1. Our Agreement",
        content: (
          <div>
            <p>
              This document outlines the Terms and Conditions for the sale of
              goods by Quality Fast Specs Ltd, located at 101 First Floor, Suite
              One, Frodingham Road, Scunthorpe DN15 7JU. These terms, along with
              your order form, represent the entire agreement between us ("we"
              or "us") and you, the customer. By placing an order and making
              full or partial payment, you enter into a legally binding
              agreement under UK law.
            </p>
          </div>
        ),
      },
      {
        id: "sale-conditions",
        title: "2. Sale Conditions",
        content: (
          <div>
            <p className="mb-4">
              By placing an order with Quality Fast Specs Ltd, you confirm the
              following:
            </p>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>2.1</strong> You are at least 16 years old.
              </li>
              <li>
                <strong>2.2</strong> You possess a valid prescription from a
                qualified optician that was issued within the last two years. A
                copy must be provided upon request.
              </li>
              <li>
                <strong>2.3</strong> You will provide precise details of your
                current prescription, including any annotations, when asked.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "pricing-payment",
        title: "3. Pricing & Payment",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>3.1</strong> The total cost for the items you order is
                specified at the time of purchase, including any shipping and
                insurance fees indicated in the order form.
              </li>
              <li>
                <strong>3.2</strong> Full payment, including any applicable
                shipping and insurance costs, is required before we can accept
                your order.
              </li>
              <li>
                <strong>3.3</strong> The stated prices include any applicable
                Value Added Tax (VAT) that Quality Fast Specs Ltd is required to
                remit to tax authorities.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "delivery-ownership",
        title: "4. Delivery & Ownership",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>4.1</strong> Goods will be delivered as per your order,
                with ownership transferred upon full payment.
              </li>
              <li>
                <strong>4.2</strong> Quality Fast Specs Ltd provides a
                complimentary postal service for sending glasses to us; however,
                this service is untracked. We recommend using a recorded
                delivery option for tracking purposes.
              </li>
              <li>
                <strong>4.3</strong> You will receive an email notification when
                your goods are dispatched back to you.
              </li>
              <li>
                <strong>4.4</strong> Quality Fast Specs Ltd will not cover
                international shipping costs for sending glasses to or from the
                UK, including for new orders and returns.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "liability",
        title: "5. Liability",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-4">
              <li>
                <strong>5.1</strong> You assume risk for the goods as soon as
                they are delivered.
              </li>
              <li>
                <strong>5.2</strong> The process of reglazing lenses into frames
                is done at your own risk. While we handle thousands of frames
                weekly with minimal issues, damage can occasionally occur. If
                damage happens, you have the following options:
                <ul className="list-disc pl-6 space-y-2 mt-2">
                  <li>
                    a. A complimentary repair of your frame (if feasible).
                  </li>
                  <li>
                    b. Purchase a new alternative frame from our stock at the
                    cost price. For example, if a designer frame retails for
                    £190, and we source it for £70, you'd pay just £70 without
                    any markup.
                  </li>
                  <li>
                    c. If the above options aren't suitable, you are entitled to
                    a full refund of your reglazing order, and we will return
                    your frame.
                  </li>
                </ul>
              </li>
            </ul>

            <div className="bg-yellow-50 p-4 rounded-lg my-4 border border-yellow-100">
              <p className="mb-2">
                In the rare event that a new frame fails during the reglazing
                process, it should be returned to the original supplier as it
                would be classified as faulty. Most new frames come with a
                12-month manufacturer warranty, which protects you against
                defects. Rest assured, we will assist you in communicating with
                the supplier if required.
              </p>
              <p>
                It's important to note that our non-liability policy regarding
                reglazing damage is standard across the industry. Many opticians
                avoid offering this service due to inherent risks, and if they
                do, it's typically under a similar policy.
              </p>
            </div>

            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>5.3</strong> We will not be responsible for any
                inconvenience, disappointment, or indirect loss as a result of
                issues related to the goods. Our only liability for compensation
                will be limited to refunds provided under these terms. This does
                not affect your statutory rights.
              </li>
              <li>
                <strong>5.4</strong> We will not be liable for delays or
                failures in delivery or for any damage caused by circumstances
                beyond our reasonable control, including strikes and industrial
                disputes.
              </li>
              <li>
                <strong>5.5</strong> During the reglazing process, the removal
                of original or demo lenses may sometimes result in damage. While
                we strive to return your original lenses in the same condition,
                we cannot guarantee this, and are not liable for any damage
                incurred.
              </li>
              <li>
                <strong>5.6</strong> We do not handle the repair of frames with
                electronic components. Any issues regarding the functionality of
                these components should be addressed with the original frame
                supplier.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "general-provisions",
        title: "6. General Provisions",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>6.1</strong> If any part of these terms is found to be
                invalid, illegal, or unenforceable, other provisions shall
                remain unaffected. This agreement is governed by UK law.
              </li>
              <li>
                <strong>6.2</strong> We reserve the right to refuse service to
                any customer if we believe it is in the best interest of the
                company.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "vat",
        title: "7. VAT",
        content: (
          <div>
            <p>All prices include VAT at the current rate.</p>
          </div>
        ),
      },
      {
        id: "returns-refunds",
        title: "8. Returns & Refunds",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-3">
              <li>
                <strong>8.1 General Return Policy:</strong> Quality Fast Specs
                Ltd has a <strong>30-day return policy</strong>. Customers must
                notify us of any issues within <strong>14 days</strong> and
                return the goods within <strong>30 days</strong>.
              </li>
              <li>
                <strong>8.2 Condition for Returns:</strong> Returned items must
                be in their original condition. We recommend obtaining proof of
                postage (e.g., recorded or signed-for delivery) as we cannot be
                responsible for lost packages.
              </li>
              <li>
                <strong>8.3 Refunds and Replacements:</strong> Upon receiving
                returned items, we will inspect them and notify you via email
                regarding your refund or replacement status. We reserve the
                right to deny a refund or replacement if the items have been
                damaged post-delivery, misused, or if the issues arise from
                normal wear and tear.
              </li>
              <li>
                <strong>8.4 Proof of Return:</strong> It is highly recommended
                that you obtain proof of postage when returning an item. We
                cannot be held liable for items lost in transit without this
                proof.
              </li>
              <li>
                <strong>8.5 Non-Refundable Items:</strong> Please note that
                delivery charges are non-refundable for international orders
                dispatched outside the United Kingdom. Additionally, all frames
                purchased through our walk-in showroom or online are
                non-refundable. Our standard{" "}
                <strong>30-day return policy</strong> applies exclusively to
                lens reglazing. Delivery charges will also not be refunded if we
                cannot process an order due to missing information that was
                requested by Quality Fast Specs prior to shipping. This applies
                to instances where customers fail to provide valid prescription
                information or fitting measurements, particularly for
                prescription orders for children under 16, as we cannot
                accommodate those services.
              </li>
              <li>
                <strong>8.6 Return Shipping for International Orders:</strong>{" "}
                Quality Fast Specs offers prepaid, tracked return labels for
                customers within the UK. Customers returning orders from outside
                the UK must bear the cost of return shipping. Delivery charges
                are non-refundable for such international orders.
              </li>
              <li>
                <strong>8.7 Statutory Rights:</strong> Your statutory rights
                regarding returns and refunds remain unaffected.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "requesting-pack",
        title: "9. Requesting a Pack",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>9.1</strong> By requesting a pack, you acknowledge that
                you have read and agree to our full terms and conditions.
              </li>
              <li>
                <strong>9.2</strong> The <strong>Request a Pack</strong> service
                is available exclusively to customers in the UK. International
                customers are responsible for the return shipping costs for
                their glasses, regardless of whether they continue with their
                order.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "promotions",
        title: "10. Promotions and Discounts",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>10.1</strong> Promotional offers are not retroactive and
                apply only to orders placed within the specified promotional
                period.
              </li>
              <li>
                <strong>10.2</strong> Voucher codes advertised online or via
                email are for single use only, applicable per customer.
                Discounts can vary, ranging from £5.00 to £49.00, depending on
                the chosen lens, unless otherwise indicated. These discounts
                apply to one pair of glasses only and cannot be combined with
                multiple pairs.
              </li>
              <li>
                <strong>10.3</strong> Discounts cannot be used in conjunction
                with other promotional vouchers unless explicitly stated, but
                can be combined with our <strong>OptiMax Warranty</strong>{" "}
                upgrade for select products.
              </li>
              <li>
                <strong>10.4</strong> Discounts only apply to our in-house
                lenses as listed on our website, excluding frames, branded
                lenses, and repair services unless specifically mentioned in
                promotions.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "competitions",
        title: "11. Competitions",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>11.1</strong> Competitions are limited to entries from
                the UK. Winners will be announced solely through official
                Quality Fast Specs social media accounts. Never provide personal
                banking information to unofficial accounts.
              </li>
              <li>
                <strong>11.2</strong> Prize claims must be made within three
                working days after the announcement. Failure to do so will
                result in the prize becoming void, allowing another winner to be
                selected.
              </li>
              <li>
                <strong>11.3</strong> Quality Fast Specs reserves the right to
                use any user-submitted content shared via email or social media
                for promotional purposes related to competitions and marketing
                initiatives.
              </li>
            </ul>
          </div>
        ),
      },
      {
        id: "storage",
        title: "12. Storage Policy",
        content: (
          <div>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                <strong>12.1</strong> If Quality Fast Specs retains your glasses
                for over six months without an order being placed, a fee of
                £4.35 for standard tracked delivery will be applied to return
                your frames, even if you do not proceed with an order.
              </li>
              <li>
                <strong>12.2</strong> After six months of inactivity from a
                customer, Quality Fast Specs may dispose of the frames. While we
                will make efforts to retain them as long as possible, storage
                space is limited, and frames may be archived and recycled after
                this period.
              </li>
            </ul>
          </div>
        ),
      },
    ];

    return (
      <div>
        <div className="bg-primary/10 p-4 rounded-lg mb-6 border border-primary/25">
          <div className="flex items-start">
            <ShieldCheck className="text-primary/60 mr-3 mt-1" size={24} />
            <div>
              <h3 className="font-semibold text-lg mb-2">
                Reglazing Service Terms & Conditions
              </h3>
              <p>
                These terms govern our frame reglazing service. Please read them
                carefully before placing an order. By using our reglazing
                service, you agree to these terms in their entirety.
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {sections.map((section) => (
            <div key={section.id} className="border rounded-lg overflow-hidden">
              <button
                onClick={() => toggleSection(section.id)}
                className="w-full px-4 py-3 text-left font-semibold flex justify-between items-center hover:bg-gray-50"
              >
                {section.title}
                <span className="text-xl">
                  {activeSection === section.id ? "−" : "+"}
                </span>
              </button>
              {activeSection === section.id && (
                <div className="p-4 bg-white border-t">{section.content}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Wrapper vertical className=" bg-white">
      <div className="mb-8 text-center border-b pb-6">
        <h1 className="text-3xl font-bold mb-4">Return Policies</h1>
        <p className="text-lg max-w-3xl mx-auto">
          We want you to be completely satisfied with your purchase. Review our
          detailed return policies for both new glasses and our reglazing
          service.
        </p>
      </div>

      <div className="mb-6">
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab("glasses")}
            className={`flex-1 py-3 px-4 text-center font-semibold ${
              activeTab === "glasses"
                ? "border-b-2 border-primary/60 text-primary/60"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            Glasses Return Policy
          </button>
          <button
            onClick={() => setActiveTab("reglazing")}
            className={`flex-1 py-3 px-4 text-center font-semibold ${
              activeTab === "reglazing"
                ? "border-b-2 border-primary/60 text-primary/60"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            Reglazing Return Policy
          </button>
        </div>
      </div>

      <div>
        {activeTab === "glasses"
          ? renderGlassesPolicy()
          : renderReglazingPolicy()}
      </div>

      <div className="mt-8 pt-6 border-t flex justify-between">
        <button
          onClick={() => setActiveTab("glasses")}
          className={`flex items-center px-4 py-2 rounded-lg ${
            activeTab === "reglazing"
              ? "text-primary/60 hover:bg-primary/10"
              : "text-gray-400 cursor-not-allowed"
          }`}
          disabled={activeTab === "glasses"}
        >
          <ArrowLeft size={16} className="mr-2" />
          Glasses Return Policy
        </button>
        <button
          onClick={() => setActiveTab("reglazing")}
          className={`flex items-center px-4 py-2 rounded-lg ${
            activeTab === "glasses"
              ? "text-primary/60 hover:bg-primary/10"
              : "text-gray-400 cursor-not-allowed"
          }`}
          disabled={activeTab === "reglazing"}
        >
          Reglazing Return Policy
          <ArrowRight size={16} className="ml-2" />
        </button>
      </div>
    </Wrapper>
  );
};

export default ReturnPolicies;
