import type { Metada<PERSON> } from "next";
import { <PERSON> } from "next/font/google";
import "./globals.css";
import { Navbar } from "@/components/common/navbar";
import { Footer } from "@/components/common/footer";
import { StoreProvider } from "@/store/store-provider";
import { Toaster } from "@/components/ui/sonner";

const albertSans = Albert_San<PERSON>({
  variable: "--font-albert-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Quality Fast Specs",
  description: "Quality Fast Specs",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  
  return (
    <html lang="en">
      <body className={`${albertSans.variable} antialiased`}>
        <div className=" bg-white text-gray-900 min-h-screen font-albertSans">
          <StoreProvider>
            <Toaster />
            <>
              <Navbar />
              {children}
              <Footer />
            </>
          </StoreProvider>
        </div>
      </body>
    </html>
  );
}
