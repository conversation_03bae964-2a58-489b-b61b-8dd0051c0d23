"use client";

import { cn } from "@/lib/utils";
import { CheckCircle, CreditCard, Home, ShoppingBag, Truck } from "lucide-react";
import Link from "next/link";
import React from "react";

interface CheckoutStepsProps {
  currentStep: number;
}

export const CheckoutSteps: React.FC<CheckoutStepsProps> = ({ currentStep }) => {
  const steps = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      href: "/checkout",
      icon: ShoppingBag,
    },
    {
      id: 2,
      name: "Address",
      href: "/checkout/address",
      icon: Home,
    },
    {
      id: 3,
      name: "Payment",
      href: "/checkout/payment",
      icon: CreditCard,
    },
    {
      id: 4,
      name: "Confirmation",
      href: "/checkout/confirmation",
      icon: CheckCircle,
    },
  ];

  return (
    <div className="w-full py-6">
      <div className="flex items-center justify-center">
        <div className="w-full max-w-3xl">
          <div className="flex items-center">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="relative flex flex-col items-center">
                  <div
                    className={cn(
                      "flex h-12 w-12 items-center justify-center rounded-full border-2 text-base font-semibold",
                      currentStep > step.id
                        ? "border-primary bg-primary text-white"
                        : currentStep === step.id
                        ? "border-primary text-primary"
                        : "border-gray-300 text-gray-300"
                    )}
                  >
                    {currentStep > step.id ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <step.icon className="h-6 w-6" />
                    )}
                  </div>
                  <p
                    className={cn(
                      "mt-2 text-xs font-medium",
                      currentStep >= step.id ? "text-primary" : "text-gray-400"
                    )}
                  >
                    {step.name}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "h-0.5 w-full flex-1",
                      currentStep > index + 1 ? "bg-primary" : "bg-gray-200"
                    )}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
