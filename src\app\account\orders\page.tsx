"use client";

import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState, useAppDispatch } from "@/store/store";
import { fetchOrders } from "@/store/slices/order-slice";
import { Wrapper } from "@/components/common/wrapper";
import { Button } from "@/components/ui/button";
import { Info, Loader } from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/utils/format-price";
import { toast } from "sonner";
import { ButtonDropdown } from "@/components/ui/button-dropdown";
import { Pagination } from "@/components/ui/pagination";
import { getImageUrl } from "@/utils/image-url";

export default function OrdersPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const dispatch = useAppDispatch();
  const { orders, loading, error, paginator } = useSelector(
    (state: RootState) => state.order
  );
  const [days, setDays] = useState(7);
  const [filterLabel, setFilterLabel] = useState("Past 7 Days");
  const { user } = useSelector((state: RootState) => state.user);

  const handleAction = (action: number, label: string) => {
    setDays(action);
    setFilterLabel(label);
  };

  const filters = [
    {
      label: "Past 7 Days",
      onClick: () => handleAction(7, "Past 7 Days"),
    },
    {
      label: "Past 1 Month",
      onClick: () => handleAction(30, "Past 1 Month"),
    },
    {
      label: "Past 3 Months",
      onClick: () => handleAction(90, "Past 3 Months"),
    },
  ];

  useEffect(() => {
    if (user) {
      loadOrders();
    }
  }, [dispatch, user, currentPage, days]);

  const loadOrders = () => {
    dispatch(fetchOrders({ page: currentPage, limit: 10, days: days }))
      .unwrap()
      .catch((error) => {
        toast.error(error.message || "Failed to load orders");
      });
  };

  function getPageSeries(currentPage: number, length: number = 5): number[] {
    const { limit, page, total, totalPages } = paginator;
    const start = currentPage;
    const end = Math.min(currentPage + length - 1, totalPages);

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  if (loading) {
    return (
      <Wrapper vertical>
        <div className="flex justify-center items-center py-20">
          <Loader className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2">Loading orders...</span>
        </div>
      </Wrapper>
    );
  }

  if (error) {
    return (
      <Wrapper vertical>
        <div className="text-center py-10">
          <p className="text-red-500">{error}</p>
          <Button onClick={loadOrders} className="mt-4">
            Try Again
          </Button>
        </div>
      </Wrapper>
    );
  }

  return (
    <Wrapper vertical>
      <h1 className="text-2xl font-bold mb-6">Your Orders</h1>
      <div className="flex justify-end mb-4">
        <ButtonDropdown
          label={filterLabel}
          variant="outline"
          size="sm"
          className="rounded-sm"
          buttonClassName="bg-white font-semibold border-gray-200"
          items={filters}
        />
      </div>

      {orders.length === 0 ? (
        <div className="text-center py-10">
          <p>You haven't placed any orders yet.</p>
          <Link href="/">
            <Button className="mt-4">Start Shopping</Button>
          </Link>
        </div>
      ) : (
        <>
          <div className="space-y-8">
            {orders.map((order, index) => {
              return (
                <div
                  key={"order-" + index}
                  className="rounded-sm border border-gray-200 px-10 pt-3 pb-14"
                >
                  {/* ORDER INFO */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-6 border-b border-gray-200 pb-4">
                    <div className="text-sm font-semibold space-y-2">
                      <h2 className="text-gray-400">Order ID:</h2>
                      <p>#{order.orderId}</p>
                    </div>

                    <div className="text-sm font-semibold space-y-2">
                      <h2 className="text-gray-400">Order Date:</h2>
                      <p>{new Date(order.createdAt).toLocaleDateString()}</p>
                    </div>

                    <div className="text-sm font-semibold space-y-2">
                      <h2 className="text-gray-400">Total Amount:</h2>
                      <p>{formatPrice({ price: order.totalAmount })}</p>
                    </div>

                    <div className="text-sm font-semibold space-y-2">
                      <h2 className="text-gray-400">Status:</h2>
                      <p className="capitalize text-primary">{order.status}</p>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:gap-2 lg:col-span-2 xl:justify-end">
                      <Button
                        className="w-full sm:w-auto py-1 px-5 rounded-sm bg-white font-semibold text-black shadow-none border border-gray-200 hover:bg-gray-50"
                        size="sm"
                      >
                        View Invoice
                      </Button>

                      <Button
                        className="w-full sm:w-auto mt-2 sm:mt-0 rounded-sm font-semibold hover:text-white shadow-none"
                        size="sm"
                      >
                        <Link
                          className="py-1 px-5"
                          href={`/account/orders/details/${order._id}`}
                        >
                          View Order
                        </Link>
                      </Button>
                    </div>
                  </div>

                  {/* ORDER ITEMS */}
                  <div className="space-y-7 mt-6">
                    <p className="flex items-center text-sm text-gray-400 font-semibold mb-6">
                      <Info size={15} className="mr-1 text-primary" />
                      Estimated Delivery {order.delivery}
                    </p>

                    {order.items.map((item, index) => {
                      return (
                        <div
                          key={"order-item-" + index}
                          className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 xl:grid-cols-9 gap-6`}
                        >
                          <div className="bg-gray-100 w-24 h-24 flex justify-center items-center">
                            <img
                              className="h-auto py-6 px-2"
                              src={getImageUrl(item.product.thumbnail)}
                              alt=""
                            />
                          </div>

                          <div
                            className={`col-span-8 space-y-4 w-100  ${
                              index !== order.items?.length - 1
                                ? "border-b border-gray-200 pb-6"
                                : ""
                            }`}
                          >
                            <div className="flex justify-between">
                              <p>
                                <Link href={`/product/${item.product.slug}`}>
                                  {item.product.name}
                                </Link>
                              </p>

                              <p>
                                {formatPrice({
                                  price: item.product.price || 0,
                                })}
                              </p>
                            </div>

                            <span className="whitespace-pre text-gray-600">
                              Quantity:{" "}
                              <span className="font-semibold">
                                {item.quantity}
                              </span>
                            </span>
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                              <div className="grid grid-cols-1 gap-y-3 gap-x-9 lg:grid-cols-2 xl:grid-cols-3 text-gray-400 text-sm">
                                {item.variation?.attribute.map((att) => {
                                  return (
                                    <span
                                      key={att}
                                      className="whitespace-pre uppercase"
                                    >
                                      {att.split("-")[0]}:
                                      <span className="font-semibold text-black ml-1">
                                        {att.split("-").slice(1).join(" ")}
                                      </span>
                                    </span>
                                  );
                                })}
                              </div>

                              <div className="justify-center lg:justify-end flex font-semibold text-sm">
                                <Link
                                  className="text-primary"
                                  href={`/product/${item.product.slug}`}
                                >
                                  View Product
                                </Link>
                                <span className="text-primary mx-1">|</span>
                                <Link
                                  className="text-primary"
                                  href={`/product/${item.product.slug}`}
                                >
                                  Buy Again
                                </Link>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="flex justify-center mt-4">
            <Pagination
              currentPage={currentPage}
              totalPages={paginator.totalPages}
              buttonClassName="rounded-full"
              onPageChange={(page) => setCurrentPage(page)}
            />
          </div>
        </>
      )}
    </Wrapper>
  );
}
