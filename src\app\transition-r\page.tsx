import { Wrapper } from "@/components/common/wrapper";
import Image from "next/image";
import React from "react";

const page = () => {
  const data = [
    {
      title: "Transitions® GEN S™ ultra-dynamic lenses",
      banner: "/transition-r/banner/gens.png",
      colorsHeader: "Available in 8 colours",
      colors: [
        {
          title: "Ruby",
          image: "/transition-r/lenses/ruby.svg",
        },
        {
          title: "Sapphire",
          image: "/transition-r/lenses/sapphire.svg",
        },
        {
          title: "Amethyst",
          image: "/transition-r/lenses/amethyst.svg",
        },
        {
          title: "Emerald",
          image: "/transition-r/lenses/emerald.svg",
        },
        {
          title: "Amber",
          image: "/transition-r/lenses/amber.svg",
        },
        {
          title: "Grey",
          image: "/transition-r/lenses/gray.svg",
        },
        {
          title: "Brown",
          image: "/transition-r/lenses/brown.svg",
        },
        {
          title: "Green",
          image: "/transition-r/lenses/green.svg",
        },
      ],
      otherAreas: [
        {
          title: "Fully Clear Indoors",
          image: "/transition-r/other/home.svg",
        },
        {
          title: "Darkens In Seconds",
          image: "/transition-r/other/sunny.svg",
        },
        {
          title: "Amazingly Fast",
          image: "/transition-r/other/summertime.svg",
        },
        {
          title:
            "Blocks 100% UVA & UVB rays filters blue-violet light indoors & outdoors",
          image: "/transition-r/other/shield.svg",
        },
      ],
      description: [
        "New technology, new frontier of performance. Transitions® GEN S™ They are ultra-responsive to light, darkening outdoors in seconds1 and returning to clear faster than ever, making them the most responsive Transitions lenses.",
        "These dynamic lenses are available in eight different colours, including amber, amethyst, brown, emerald, green, grey, sapphire and the new addition, ruby. Simply choose Transitions® GEN S™ as your lens type and the new colours will appear, ready for you to choose which option you prefer.",
      ],
    },
    {
      title: "Transitions® XTRActive®",
      banner: "/transition-r/banner/xtractive.png",
      colorsHeader: "Available in 3 colours",
      colors: [
        {
          title: "Grey",
          image: "/transition-r/lenses/gray.svg",
        },
        {
          title: "Brown",
          image: "/transition-r/lenses/brown.svg",
        },
        {
          title: "Green",
          image: "/transition-r/lenses/green.svg",
        },
      ],
      otherAreas: [
        {
          title: "Clear with a hint of protective tint indoors",
          image: "/transition-r/other/home.svg",
        },
        {
          title: "The darkest in hot temperatures",
          image: "/transition-r/other/sunny.svg",
        },
        {
          title:
            "Blocks 100% UVA & UVB rays filters blue-violet light indoors & outdoors",
          image: "/transition-r/other/shield.svg",
        },
        {
          title: "The darkest in the car",
          image: "/transition-r/other/solar-car.svg",
        },
      ],
      description: [
        "New technology, new frontier of performance. Transitions® GEN S™ They are ultra-responsive to light, darkening outdoors in seconds1 and returning to clear faster than ever, making them the most responsive Transitions lenses.",
        "These dynamic lenses are available in eight different colours, including amber, amethyst, brown, emerald, green, grey, sapphire and the new addition, ruby. Simply choose Transitions® GEN S™ as your lens type and the new colours will appear, ready for you to choose which option you prefer.",
      ],
    },
    {
      title: "Transitions® Drivewear®",
      banner: "/transition-r/banner/drivewear.png",
      colorsHeader: "Transitions From Green to Brown",
      colors: [
        {
          title: "",
          image: "/transition-r/lenses/green-brown.svg",
        },
      ],
      otherAreas: [
        {
          title: "Clear with a hint of protective tint indoors",
          image: "/transition-r/other/protective-hint.svg",
        },
        {
          title: "The darkest in hot temperatures",
          image: "/transition-r/other/sunny.svg",
        },
        {
          title:
            "Blocks 100% UVA & UVB rays filters blue-violet light indoors & outdoors",
          image: "/transition-r/other/shield.svg",
        },
        {
          title: "The darkest in the car",
          image: "/transition-r/other/solar-car.svg",
        },
      ],
      description: [
        "New technology, new frontier of performance. Transitions® GEN S™ They are ultra-responsive to light, darkening outdoors in seconds1 and returning to clear faster than ever, making them the most responsive Transitions lenses.",
        "These dynamic lenses are available in eight different colours, including amber, amethyst, brown, emerald, green, grey, sapphire and the new addition, ruby. Simply choose Transitions® GEN S™ as your lens type and the new colours will appear, ready for you to choose which option you prefer.",
      ],
    },
    {
      title: "Transitions® Drivewear®",
      banner: "/transition-r/banner/drivewear.png",
      colorsHeader: "Transitions From Green to Brown",
      colors: [
        {
          title: "Amber",
          image: "/transition-r/lenses/amber.svg",
        },
        {
          title: "Grey",
          image: "/transition-r/lenses/gray.svg",
        },
        {
          title: "Brown",
          image: "/transition-r/lenses/brown.svg",
        },
        {
          title: "Green",
          image: "/transition-r/lenses/green.svg",
        },
      ],
      otherAreas: [
        {
          title: "Fully Clear Indoors",
          image: "/transition-r/other/home.svg",
        },
        {
          title: "Darkens In Seconds",
          image: "/transition-r/other/sunny.svg",
        },
        {
          title: "Amazingly Fast",
          image: "/transition-r/other/summertime.svg",
        },
        {
          title:
            "Blocks 100% UVA & UVB rays filters blue-violet light indoors & outdoors",
          image: "/transition-r/other/shield.svg",
        },
      ],
      description: [
        "New technology, new frontier of performance. Transitions® GEN S™ They are ultra-responsive to light, darkening outdoors in seconds1 and returning to clear faster than ever, making them the most responsive Transitions lenses.",
        "These dynamic lenses are available in eight different colours, including amber, amethyst, brown, emerald, green, grey, sapphire and the new addition, ruby. Simply choose Transitions® GEN S™ as your lens type and the new colours will appear, ready for you to choose which option you prefer.",
      ],
    },
  ];
  return (
    <Wrapper vertical className="w-[90%] mx-auto space-y-5">
      {data.map((dt, index) => (
        <div key={index}>
          <p className="text-center text-2xl font-semibold mb-5">{dt.title}</p>
          <Image
            src={dt.banner}
            alt="banner"
            width={1000}
            height={1000}
            className="w-full"
          />
          <p className="text-center text-2xl font-semibold mt-5">
            {dt.colorsHeader}
          </p>
          <div className="flex flex-wrap justify-between">
            {dt.colors.map((clr) => (
              <div key={clr.title} className="flex flex-col items-center relative">
                <Image
                  src={clr.image}
                  alt="image"
                  width={100}
                  height={100}
                  className="w-full"
                />
                <p className="font-semibold absolute bottom-3">{clr.title}</p>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 bg-gray-200 my-8 px-5 py-8 gap-5">
            {dt.otherAreas.map((otherArea) => (
              <div key={otherArea.title} className="flex flex-col items-center">
                <Image
                  src={otherArea.image}
                  alt="image"
                  width={100}
                  height={100}
                  className=""
                />
                <p className="text-gray-700 font-semibold text-center">{otherArea.title}</p>
              </div>
            ))}
          </div>
          <div className="flex flex-col gap-3">
            {dt.description.map((desc, index) => (
              <p key={index}>{desc}</p>
            ))}
          </div>
        </div>
      ))}
    </Wrapper>
  );
};

export default page;
