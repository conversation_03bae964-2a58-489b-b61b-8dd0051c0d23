// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { ITax, ITaxSlice } from "@/interfaces";

// CONSTANTS
import { TAX_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";
import { getErrorMessage } from "@/utils/error-message";

const initialState: ITaxSlice = {
  taxes: [],
  loading: true,
  error: "",
};

export const fetchTaxes = createAsyncThunk<ITax[], void, FetchDataRejected>(
  "taxes/fetch",
  async (_, { rejectWithValue }) => {
    return api_interceptor
      .get(TAX_URL)
      .then((res) => {
        return res.data.data;
      })
      .catch((err) => {
        return rejectWithValue({
          message: getErrorMessage(err, "Something went wrong"),
        });
      });
  }
);

const taxesSlice = createSlice({
  name: "tax",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchTaxes.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchTaxes.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.taxes = payload;
    });
    builder.addCase(fetchTaxes.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default taxesSlice.reducer;
