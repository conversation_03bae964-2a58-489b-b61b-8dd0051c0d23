"use client";
import { NewsLetter } from "@/app/(home)/_components/newsletter";
import { redirect, useSearchParams } from "next/navigation";
import { Suspense, useEffect } from "react";
import store, { RootState } from "@/store/store";
import { resetAuthState, verifyEmail } from "@/store/slices/auth-slice";
import { useSelector } from "react-redux";
import { ToastErrors } from "@/components/common/toast-errors";

function Page() {
  const params = useSearchParams();
  const token = params.get("token") || "";
  const { error, success } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!token || success) redirect("/auth");
  }, [success, token]);

  useEffect(() => {
    if (token) {
      store.dispatch(verifyEmail({ token: token }));
    }
  }, [token]);

  useEffect(() => {
    if (error?.length || success) {
      store.dispatch(resetAuthState());
    }
  }, [error, success]);

  return (
    <>
      <ToastErrors error={error} success={success} />
      <div className="space-y-6">
        <div className="flex items-center justify-center px-4">
          <div
            className={`w-full max-w-md sm:max-w-lg lg:max-w-xl space-y-6 bg-white rounded-md shadow py-8 mt-28`}
          >
            <div className="px-6 sm:px-8">
              <h2 className="text-center text-xl font-semibold mb-3 animate-pulse">
                Verifying Your Email Address
              </h2>

              {/* LOADING */}
              <div className="h-10 w-10 border-dashed border-2 border-primary mx-auto duration-500 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>

        <NewsLetter />
      </div>
    </>
  );
}

export default function LoginFormPage() {
  return (
    // You could have a loading skeleton as the `fallback` too
    <Suspense>
      <Page />
    </Suspense>
  );
}
