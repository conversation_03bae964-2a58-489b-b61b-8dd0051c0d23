import React from "react";

export const SalesMarque = () => {
  const sales = [
    "Upto 75% off the Hight Street",
    "100% Satisfaction Guarantee",
    "Free shipping on orders over $50",
    "Exclusive events and promotions",
    "Unbeatable prices on designer brands",
    "Upto 75% off the Hight Street",
    "100% Satisfaction Guarantee",
    "Free shipping on orders over $50",
    "Exclusive events and promotions",
    "Unbeatable prices on designer brands",
  ];
  return (
    <div className="relative flex overflow-x-hidden">
      <div className="bg-[#FCFFB2] animate-marquee whitespace-nowrap">
        {sales.map((sale, index) => (
          <span key={index} className="text-center p-4 text-sm">
            {sale}
          </span>
        ))}
      </div>

      <div className="bg-[#FCFFB2] absolute top-0 animate-marquee2 whitespace-nowrap">
        {sales.map((sale, index) => (
          <span
            key={"duplicate-sales-" + index}
            className="text-center p-4 text-sm"
          >
            {sale}
          </span>
        ))}
      </div>
    </div>
  );
};
