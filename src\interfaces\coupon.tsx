export interface ICouponResponse {
  data: ICoupon;
  message: string;
}

export interface ICoupon {
  _id: string;
  code: string;
  discountType: "percentage" | "fixed";
  discountValue: number;
  maxDiscount?: number;
  minPurchaseAmount?: number;
  validUntil: string;
  isActive: boolean;
  couponType: string;
  ranges: {
    start: number | string;
    end: number | string;
    discountValue: number;
    discountType: string;
  }[];
  lastRange: {
    start: number | string;
    end: number | string;
    discountValue: number;
    discountType: string;
    apply: boolean;
  } | null;
}
