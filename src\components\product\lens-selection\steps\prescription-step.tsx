"use client";

import React, { useState, useMemo, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { LensSelectionData } from "../lens-wizard";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup } from "@/components/ui/radio-group";
import { PlusCircle, History, Clock, Check, ChevronDown } from "lucide-react";
import { Select } from "@/components/ui/select";
import { z } from "zod";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { isDuplicatePrescription } from "@/utils/check-duplicate-prescription";
import { MoreOption, PrismData } from "@/components/common/more-option";

// Create validation schema
const validationSchema = z.object({
  name: z.string().min(1, "Prescription name is required"),
  rightEye: z.object({
    sph: z.string().min(1, "SPH is required"),
    cyl: z.string(),
    axis: z.string(),
    add: z.string(),
  }),
  leftEye: z.object({
    sph: z.string().min(1, "SPH is required"),
    cyl: z.string(),
    axis: z.string(),
    add: z.string(),
  }),
  pd: z.string().optional(),

  extraInformation: z.string().optional(),
  termsAccepted: z
    .boolean()
    .refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    })
    .default(false),
});

type PrescriptionFormData = z.infer<typeof validationSchema>;
interface SavedPrescriptionFormData {
  name: string;
  rightEye: {
    sph: string;
    cyl: string;
    axis: string;
    add: string;
  };
  leftEye: {
    sph: string;
    cyl: string;
    axis: string;
    add: string;
  };
  pd: string;
  extraInformation: string;
  termsAccepted: boolean;
  moreOptions: {
    prism: boolean;
    prismData: {
      rightEye: {
        sph: string;
        cyl: string;
        add: string;
      };
      leftEye: {
        sph: string;
        cyl: string;
        add: string;
      };
    };
  };
}

interface PrescriptionStepProps {
  lensData: LensSelectionData;
  updateLensData: (data: Partial<LensSelectionData>) => void;
  onNext: () => void;
  onBack: () => void;
  operation?: string;
}

export const PrescriptionStep: React.FC<PrescriptionStepProps> = ({
  lensData,
  updateLensData,
  onNext,
  onBack,
  operation,
}) => {
  const [showPrescriptionForm, setShowPrescriptionForm] = useState(false);
  const [showSavedPrescription, setShowSavedPrescription] = useState(false);
  const [modalState, setModalState] = useState(false);
  const [isFormInitialized, setIsFormInitialized] = useState(false);

  const [addModalState, setAddModalState] = useState(false);
  const [tempSphValue, setTempSphValue] = useState({
    value: "0.00",
    position: "left",
  });
  const [prismSelected, setPrismSelected] = useState(false);
  const [prismData, setPrismData] = useState<PrismData>({
    rightEye: {
      vertical: "n/a",
      verticalBase: "n/a",
      horizontal: "n/a",
      horizontalBase: "n/a",
    },
    leftEye: {
      vertical: "n/a",
      verticalBase: "n/a",
      horizontal: "n/a",
      horizontalBase: "n/a",
    },
  });
  // Initialize form with React Hook Form
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<PrescriptionFormData>({
    resolver: zodResolver(validationSchema),
    defaultValues: {
      name: "",
      rightEye: { sph: "", cyl: "", axis: "", add: "" },
      leftEye: { sph: "", cyl: "", axis: "", add: "" },
      pd: "",

      extraInformation: "",
      termsAccepted: false,
    },
    mode: "onChange",
  });
  // Initialize edit mode
  useEffect(() => {
    if (operation === "edit" && lensData.prescription) {
      setShowPrescriptionForm(true);
      setShowSavedPrescription(false);
    }
  }, [operation, lensData.prescription]);

  // Add this useEffect after the useForm initialization
  useEffect(() => {
    if (lensData.prescription && showPrescriptionForm && !isFormInitialized) {
      setValue("name", lensData.prescription.name || "");
      setValue("rightEye.sph", lensData.prescription.rightEye?.sph || "");
      setValue("rightEye.cyl", lensData.prescription.rightEye?.cyl || "");
      setValue("rightEye.axis", lensData.prescription.rightEye?.axis || "");
      setValue("rightEye.add", lensData.prescription.rightEye?.add || "");
      setValue("leftEye.sph", lensData.prescription.leftEye?.sph || "");
      setValue("leftEye.cyl", lensData.prescription.leftEye?.cyl || "");
      setValue("leftEye.axis", lensData.prescription.leftEye?.axis || "");
      setValue("leftEye.add", lensData.prescription.leftEye?.add || "");
      setValue("pd", lensData.prescription.pd || "");
      setValue("termsAccepted", true); // Set terms as accepted for edit mode
      setIsFormInitialized(true);
    }
  }, [
    lensData.prescription,
    showPrescriptionForm,
    lensData.prescriptionType,
    setValue,
    isFormInitialized,
  ]);
  const sphValues = useMemo(() => {
    const values = [];
    for (let i = -9.75; i <= 9.75; i += 0.25) {
      const sphValue = parseFloat(i.toFixed(2));
      values.push({ label: sphValue, value: sphValue });
      if ((sphValue as any) == "0.00") {
        values.push({ label: "Plano", value: "Plano" });
      }
    }
    return values;
  }, []);
  const cylValues = useMemo(() => {
    const values = [];
    for (let i = -4.0; i <= 4.0; i += 0.25) {
      const cylValue = parseFloat(i.toFixed(2));
      values.push({ label: cylValue, value: cylValue });
      if ((cylValue as any) == "0.00") {
        values.push({ label: "SPH/DS", value: "SPH/DS" });
      }
    }
    return values;
  }, []);
  const addValues = useMemo(() => {
    const values = [];
    values.push({ label: "n/a", value: "" });
    for (let i = 0.75; i <= 3.5; i += 0.25) {
      const addValue = parseFloat(i.toFixed(2));
      values.push({ label: addValue, value: addValue });
    }
    return values;
  }, []);

  const onCancelDialog = () => {
    setModalState(false);
    if (tempSphValue.position == "left") {
      handlePrescriptionChange("leftEye", "sph", tempSphValue.value);
    } else {
      handlePrescriptionChange("rightEye", "sph", tempSphValue.value);
    }
  };

  const onConfirmDialog = () => {
    setModalState(false);
  };

  const onCloseAddDialog = () => {
    setAddModalState(false);
  };

  // Mock saved prescriptions
  const savedPrescriptions = [
    {
      id: "rx1",
      name: "My Rx 1",
      date: "01/01/2023",
      rightEye: {
        sph: "0.00",
        cyl: "0.75",
        axis: "2",
        add: "0.00",
        pd: "63",
      },
      leftEye: {
        sph: "0.00",
        cyl: "0.00",
        axis: "",
        add: "0.00",
      },
    },
  ];

  const prescriptionTypes = [
    {
      id: "new",
      name: "Enter new prescription",
      description: "Add a new prescription manually",
      icon: PlusCircle,
    },
    {
      id: "saved",
      name: "Use saved prescription",
      description: "Choose from your saved prescriptions",
      icon: History,
    },
    {
      id: "later",
      name: "Send prescription later",
      description: "You can email or upload your prescription after checkout",
      icon: Clock,
    },
  ];

  const handleSelectPrescriptionType = (type: string) => {
    updateLensData({ prescriptionType: type });

    if (type === "new") {
      setShowPrescriptionForm(true);
      setShowSavedPrescription(false);

      // Initialize with default values to ensure prescription object exists
      if (!lensData.prescription) {
        updateLensData({
          prescription: {
            name: "My Prescription",
            rightEye: { sph: "0.00", cyl: "0.00", axis: "", add: "" },
            leftEye: { sph: "0.00", cyl: "0.00", axis: "", add: "" },
            pd: "",
          },
        });
      }
    } else if (type === "saved" && savedPrescriptions.length > 0) {
      setShowSavedPrescription(true);
      setShowPrescriptionForm(false);
      if (!lensData.prescription) {
        updateLensData({
          prescription: savedPrescriptions[0],
        });
      }
    } else {
      setShowPrescriptionForm(false);
      setShowSavedPrescription(false);
    }
  };

  const handleSelectSavedPrescription = (prescription: any) => {
    updateLensData({
      prescription: {
        name: prescription.name,
        rightEye: prescription.rightEye,
        leftEye: prescription.leftEye,
      },
    });
  };

  const handlePrescriptionChange = (
    eye: "rightEye" | "leftEye",
    field: string,
    value: string
  ) => {
    if (!lensData.prescription) {
      return;
    }

    updateLensData({
      prescription: {
        ...lensData.prescription,
        [eye]: {
          ...lensData.prescription[eye],
          [field]: value,
        },
      },
    });
  };

  const handlePrescriptionPDChange = (value: string) => {
    if (!lensData.prescription) {
      return;
    }

    updateLensData({
      prescription: {
        ...lensData.prescription,
        pd: value,
      },
    });
  };

  const handlePrismChange = (hasPrism: boolean, prismData?: PrismData) => {
    setPrismSelected(hasPrism);
    if (prismData) {
      setPrismData(prismData);
    }

    // Update the lens data with prism pricing
    const currentTotalPrice =
      (lensData.lensPackage?.price || 0) +
      (lensData.lensPackage?.subPackage?.price || 0) +
      lensData.packagePrice;

    const prismPrice = hasPrism ? 15 : 0; // $15 for prism

    updateLensData({
      totalPrice: currentTotalPrice + prismPrice,
      prismSelected: hasPrism,
      prismData: prismData,
    });
  };

  const handleSavePrescription = (data: PrescriptionFormData) => {
    // Convert PrescriptionFormData to SavedPrescriptionFormData
    const savedData: SavedPrescriptionFormData = {
      name: data.name,
      rightEye: data.rightEye,
      leftEye: data.leftEye,
      pd: data.pd || "",
      extraInformation: data.extraInformation || "",
      termsAccepted: data.termsAccepted,
      moreOptions: {
        prism: false,
        prismData: {
          rightEye: { sph: "0.00", cyl: "0.00", add: "n/a" },
          leftEye: { sph: "0.00", cyl: "0.00", add: "n/a" },
        },
      },
    };

    // const existingPrescription = JSON.parse(
    //   localStorage.getItem('prescription') || '[]'
    // );
    // if (isDuplicatePrescription(savedData, existingPrescription)) {
    //   alert('This prescription already exists in your saved prescriptions.');
    //   return;
    // }

    // Check if terms are accepted (skip for edit mode)
    if (operation !== "edit" && !savedData.termsAccepted) {
      setValue("termsAccepted", false, { shouldValidate: true });
      return;
    }

    // Custom validation for axis when CYL is provided
    let hasCustomErrors = false;

    // Check right eye
    if (
      savedData.rightEye.cyl &&
      savedData.rightEye.cyl !== "" &&
      savedData.rightEye.cyl !== "SPH/DS" &&
      savedData.rightEye.cyl !== "0.00" &&
      (!savedData.rightEye.axis || savedData.rightEye.axis.trim() === "")
    ) {
      // Set error for right eye axis
      setValue("rightEye.axis", "", {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
      // Force a re-render to show the error
      setTimeout(() => {
        const rightEyeAxisInput = document.querySelector(
          '[name="rightEye.axis"]'
        );
        if (rightEyeAxisInput) {
          rightEyeAxisInput.dispatchEvent(
            new Event("focus", { bubbles: true })
          );
          rightEyeAxisInput.dispatchEvent(new Event("blur", { bubbles: true }));
        }
      }, 0);
      hasCustomErrors = true;
    }
    // Check left eye
    if (
      savedData.leftEye.cyl &&
      savedData.leftEye.cyl !== "" &&
      savedData.leftEye.cyl !== "SPH/DS" &&
      savedData.leftEye.cyl !== "0.00" &&
      (!savedData.leftEye.axis || savedData.leftEye.axis.trim() === "")
    ) {
      // Set error for left eye axis
      setValue("leftEye.axis", "", {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
      // Force a re-render to show the error
      setTimeout(() => {
        const leftEyeAxisInput = document.querySelector(
          '[name="leftEye.axis"]'
        );
        if (leftEyeAxisInput) {
          leftEyeAxisInput.dispatchEvent(new Event("focus", { bubbles: true }));
          leftEyeAxisInput.dispatchEvent(new Event("blur", { bubbles: true }));
        }
      }, 0);
      hasCustomErrors = true;
    }
    if (hasCustomErrors) {
      // Add custom error messages
      if (
        !savedData.rightEye.axis &&
        savedData.rightEye.cyl &&
        savedData.rightEye.cyl !== "SPH/DS" &&
        savedData.rightEye.cyl !== "0.00"
      ) {
        setValue("rightEye.axis", "", {
          shouldValidate: true,
        });
      }
      if (
        !savedData.leftEye.axis &&
        savedData.leftEye.cyl &&
        savedData.leftEye.cyl !== "SPH/DS" &&
        savedData.leftEye.cyl !== "0.00"
      ) {
        setValue("leftEye.axis", "", {
          shouldValidate: true,
        });
      }
      return;
    }

    updateLensData({
      prescription: {
        name: savedData.name,
        rightEye: savedData.rightEye,
        leftEye: savedData.leftEye,
        pd: savedData.pd,
      },
    });

    // Only save to localStorage if not in edit mode
    if (operation !== "edit") {
      // Get existing prescriptions or initialize empty array
      const existingPrescriptions = JSON.parse(
        localStorage.getItem("prescription") || "[]"
      );

      // Add new prescription to the array
      const updatedPrescriptions = [...existingPrescriptions, savedData];

      // Save back to localStorage
      localStorage.setItem(
        "prescription",
        JSON.stringify(updatedPrescriptions)
      );
    }

    onNext();
  };

  const renderPrescriptionForm = () => {
    if (!showPrescriptionForm) return null;

    return (
      <div className="mt-6 border rounded-md p-4">
        <h3 className="font-medium mb-4">Enter Prescription Details</h3>

        <div className="mb-4">
          <Label htmlFor="prescription-name">Prescription Name</Label>
          <Input
            id="prescription-name"
            {...register("name")}
            className="mt-1"
          />
        </div>
        {errors.name && (
          <p className="text-sm text-red-500 mt-1">
            {errors.name.message?.toString() || "Prescription name is required"}
          </p>
        )}

        <div className="grid grid-cols-5 gap-2 mb-2 text-sm font-medium">
          <div className="col-span-1"></div>
          <div className="col-span-1">SPH</div>
          <div className="col-span-1">CYL</div>
          <div className="col-span-1">Axis</div>
          <div className="col-span-1">ADD</div>
        </div>

        <div className="grid grid-cols-5 gap-2 mb-4">
          <div className="col-span-1 flex items-center">
            <Label>OD (Right)</Label>
          </div>
          <div className="col-span-1">
            <Select
              options={sphValues}
              value={watch("rightEye.sph") || ""}
              onChange={(e) => {
                setTempSphValue({
                  position: "right",
                  value: lensData.prescription?.rightEye?.sph!,
                });
                setValue("rightEye.sph", e.target.value);
                handlePrescriptionChange("rightEye", "sph", e.target.value);
                const leftEyeSph = +(lensData.prescription?.leftEye?.sph || 0);
                const rightEyeSph = +(e.target.value || 0);
                if (
                  (leftEyeSph < 0 && rightEyeSph > 0) ||
                  (leftEyeSph > 0 && rightEyeSph < 0)
                ) {
                  setModalState(true);
                }
              }}
            />
            {errors.rightEye?.sph && (
              <p className="text-sm text-red-500 mt-1">
                {errors.rightEye.sph.message?.toString() || "SPH is required"}
              </p>
            )}
          </div>
          <div className="col-span-1">
            <Select
              options={cylValues}
              value={watch("rightEye.cyl") || ""}
              onChange={(e) => {
                setValue("rightEye.cyl", e.target.value);
                handlePrescriptionChange("rightEye", "cyl", e.target.value);
              }}
            />
            {errors.rightEye?.cyl && (
              <p className="text-sm text-red-500 mt-1">
                {errors.rightEye.cyl.message?.toString()}
              </p>
            )}
          </div>
          <div className="col-span-1">
            <Input
              value={watch("rightEye.axis") || ""}
              onChange={(e) => {
                setValue("rightEye.axis", e.target.value);
                handlePrescriptionChange("rightEye", "axis", e.target.value);
              }}
              placeholder="0"
            />
            {errors.rightEye?.axis && (
              <p className="text-sm text-red-500 mt-1">
                {errors.rightEye.axis.message?.toString() ||
                  "Axis is required when CYL is provided"}
              </p>
            )}
          </div>
          <div className="col-span-1">
            <Select
              value={watch("rightEye.add") || ""}
              onChange={(e) => {
                setValue("rightEye.add", e.target.value);
                handlePrescriptionChange("rightEye", "add", e.target.value);
                if (
                  lensData.prescription?.leftEye?.add &&
                  lensData.prescription?.leftEye?.add != e.target.value
                ) {
                  setAddModalState(true);
                }
              }}
              options={addValues}
            />
          </div>
        </div>
        <div className="grid grid-cols-5 gap-2 mb-4">
          <div className="col-span-1 flex items-center">
            <Label>OS (Left)</Label>
          </div>
          <div className="col-span-1">
            <Select
              options={sphValues}
              // {...register('leftEye.sph')}
              value={watch("leftEye.sph") || ""}
              onChange={(e) => {
                setTempSphValue({
                  position: "left",
                  value: lensData.prescription?.leftEye?.sph!,
                });
                setValue("leftEye.sph", e.target.value);
                handlePrescriptionChange("leftEye", "sph", e.target.value);

                const rightEyeSph = +(
                  lensData.prescription?.rightEye?.sph || 0
                );
                const leftEyeSph = +(e.target.value || 0);
                if (
                  (leftEyeSph < 0 && rightEyeSph > 0) ||
                  (leftEyeSph > 0 && rightEyeSph < 0)
                ) {
                  setModalState(true);
                }
              }}
            />
            {errors.leftEye?.sph && (
              <p className="text-sm text-red-500 mt-1">
                {errors.leftEye.sph.message?.toString() || "SPH is required"}
              </p>
            )}
          </div>
          <div className="col-span-1">
            <Select
              options={cylValues}
              {...register("leftEye.cyl")}
              value={watch("leftEye.cyl") || ""}
              onChange={(e) => {
                setValue("leftEye.cyl", e.target.value);
                handlePrescriptionChange("leftEye", "cyl", e.target.value);
              }}
            />
            {errors.leftEye?.cyl && (
              <p className="text-sm text-red-500 mt-1">
                {errors.leftEye.cyl.message?.toString()}
              </p>
            )}
          </div>
          <div className="col-span-1">
            <Input
              // {...register('leftEye.axis')}
              value={watch("leftEye.axis") || ""}
              onChange={(e) => {
                setValue("leftEye.axis", e.target.value);
                handlePrescriptionChange("leftEye", "axis", e.target.value);
              }}
              placeholder="0"
            />
            {errors.leftEye?.axis && (
              <p className="text-sm text-red-500 mt-1">
                {errors.leftEye.axis.message?.toString() ||
                  "Axis is required when CYL is provided"}
              </p>
            )}
          </div>
          <div className="col-span-1">
            <Select
              options={addValues}
              // {...register('leftEye.add')}
              value={watch("leftEye.add") || ""}
              onChange={(e) => {
                setValue("leftEye.add", e.target.value);
                handlePrescriptionChange("leftEye", "add", e.target.value);
                if (
                  lensData.prescription?.rightEye?.add &&
                  lensData.prescription?.rightEye?.add != e.target.value
                ) {
                  setAddModalState(true);
                }
              }}
            />
          </div>
        </div>
        <div className="grid grid-cols-5 gap-2">
          <div className="col-span-1 flex items-center">
            <Label>PD</Label>
          </div>
          <div className="col-span-1">
            <Input
              // {...register('pd')}
              value={watch("pd") || ""}
              onChange={(e) => {
                setValue("pd", e.target.value);
                handlePrescriptionPDChange(e.target.value);
              }}
              placeholder="63"
            />
            {errors.pd && (
              <p className="text-sm text-red-500 mt-1">
                {errors.pd.message?.toString()}
              </p>
            )}
          </div>
        </div>
        <MoreOption
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          onPrismChange={handlePrismChange}
        />
      </div>
    );
  };

  const renderSavedPrescriptions = () => {
    if (!showSavedPrescription) return null;
    if (lensData.prescriptionType !== "saved") return null;
    const savedPrescriptions = JSON.parse(
      localStorage.getItem("prescription") || "[]"
    );
    return (
      <>
        {savedPrescriptions.length > 0 ? (
          <div className="mt-6">
            <h3 className="font-medium mb-4">Your Saved Prescriptions</h3>
            <p className="text-sm text-gray-500 mb-4">
              *Please note any prescriptions older than 2 years are not
              available for selection
            </p>

            <div className="space-y-4">
              {savedPrescriptions.map((prescription: any, index: number) => (
                <div
                  key={index}
                  className={`p-4 border rounded-md cursor-pointer transition-all ${
                    lensData.prescription?.name === prescription.name
                      ? "border-primary bg-primary/5"
                      : "border-gray-200 hover:border-primary/50"
                  }`}
                  onClick={() => handleSelectSavedPrescription(prescription)}
                >
                  <div className="flex justify-between">
                    <div>
                      <h4 className="font-medium">{prescription.name}</h4>
                    </div>
                    {lensData.prescription?.name === prescription.name && (
                      <Check className="text-primary w-5 h-5" />
                    )}
                  </div>

                  <div className="mt-4 text-sm">
                    <div className="grid grid-cols-5 gap-2 mb-2 font-medium">
                      <div className="col-span-1"></div>
                      <div className="col-span-1">SPH</div>
                      <div className="col-span-1">CYL</div>
                      <div className="col-span-1">Axis</div>
                      <div className="col-span-1">Add</div>
                      <div className="col-span-1"></div>
                    </div>

                    <div className="grid grid-cols-5 gap-2 mb-2">
                      <div className="col-span-1">OD</div>
                      <div className="col-span-1">
                        {prescription.rightEye.sph}
                      </div>
                      <div className="col-span-1">
                        {prescription.rightEye.cyl}
                      </div>
                      <div className="col-span-1">
                        {prescription.rightEye.axis}
                      </div>
                      <div className="col-span-1">
                        {prescription.rightEye.add}
                      </div>
                    </div>

                    <div className="grid grid-cols-5 gap-2">
                      <div className="col-span-1">OS</div>
                      <div className="col-span-1">
                        {prescription.leftEye.sph}
                      </div>
                      <div className="col-span-1">
                        {prescription.leftEye.cyl}
                      </div>
                      <div className="col-span-1">
                        {prescription.leftEye.axis}
                      </div>
                      <div className="col-span-1">
                        {prescription.leftEye.add}
                      </div>
                      <div className="col-span-1"></div>
                    </div>
                    <div className="grid grid-cols-5 gap-2">
                      <div className="col-span-1">PD</div>
                      <div className="col-span-1">{prescription.pd}</div>

                      <div className="col-span-1"></div>
                    </div>
                  </div>

                  <div className="mt-4 flex justify-end">
                    <Button variant="outline" size="sm">
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <p className="text-sm font-medium text-slate-700">
            You don’t have any saved prescriptions at the moment. Once you add a
            new prescription, it will appear here for quick selection.
          </p>
        )}
      </>
    );
  };

  const canProceed = () => {
    // For "new" prescription type, check if form is shown and prescription is filled
    if (lensData.prescriptionType === "new") {
      return showPrescriptionForm && !!lensData.prescription;
    } else if (lensData.prescriptionType === "saved") {
      return !!lensData.prescription;
    } else if (lensData.prescriptionType === "later") {
      return true;
    }
    return false;
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">
        {operation === "edit" ? "Edit Prescription" : "Prescription"}
      </h2>
      <AlertDialog open={modalState} onOpenChange={() => setModalState(false)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              That's unusual! For most people, both eyes have either negative (
              - ) or positive ( + ) SPH values. Are you sure your prescription
              shows both?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={onCancelDialog}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={onConfirmDialog}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={addModalState} onOpenChange={onCloseAddDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogDescription>
              That's unusual! For most people, Addition (ADD) values are the
              same for both eyes. Please confirm your prescription indicates
              separate values under ADD. For help, please contact customer
              support.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={onCloseAddDialog}>
              GOT IT, THANKS
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {operation !== "edit" && (
        <RadioGroup
          value={lensData.prescriptionType}
          onValueChange={handleSelectPrescriptionType}
          className="space-y-4"
        >
          {prescriptionTypes.map((type) => (
            <div
              key={type.id}
              className={`p-4 border rounded-md cursor-pointer transition-all ${
                lensData.prescriptionType === type.id
                  ? "border-primary bg-primary/5"
                  : "border-gray-200 hover:border-primary/50"
              }`}
              onClick={() => handleSelectPrescriptionType(type.id)}
            >
              <div className="flex items-start">
                <div className="mr-4">
                  <type.icon
                    className={`w-5 h-5 ${
                      lensData.prescriptionType === type.id
                        ? "text-primary"
                        : "text-gray-500"
                    }`}
                  />
                </div>
                <div>
                  <Label
                    htmlFor={type.id}
                    className="font-medium cursor-pointer"
                  >
                    {type.name}
                  </Label>
                  <p className="text-sm text-gray-500 mt-1">
                    {type.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </RadioGroup>
      )}

      {renderPrescriptionForm()}

      {renderSavedPrescriptions()}

      <div className="flex justify-between mt-6">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button
          onClick={
            operation === "edit" || lensData.prescriptionType === "new"
              ? handleSubmit(handleSavePrescription)
              : () => {
                  if (canProceed()) {
                    onNext();
                  }
                }
          }
          disabled={
            operation === "edit"
              ? false
              : !lensData.prescriptionType || !canProceed()
          }
        >
          {operation === "edit" ? "Save Changes" : "Next"}
        </Button>
      </div>
    </div>
  );
};
