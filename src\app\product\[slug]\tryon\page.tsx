"use client";

import React, { useState, useEffect } from "react";
import { VirtualTryOn } from "@/components/common/virtual-try-on";
import { useParams } from "next/navigation";

const page = () => {
  const { slug } = useParams();
  const [models, setModels] = useState<{ title: string; model: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState({});

  useEffect(() => {
    return () => {};
  }, []);

  return (
    <div className="mb-6">
      <VirtualTryOn slug={slug} />
      {models.map((model, index) => (
        <p>{model.title}</p>
      ))}
    </div>
  );
};

export default page;
