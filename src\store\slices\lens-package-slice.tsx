// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { ILensPackage, ILensPackageSlice } from "@/interfaces";

// CONSTANTS
import { LENS_PACKAGE_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: ILensPackageSlice = {
  lensPackages: [],
  loading: true,
  error: "",
};

export const fetchLensPackages = createAsyncThunk<
  ILensPackage[],
  void,
  FetchDataRejected
>("lensPackages/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(LENS_PACKAGE_URL)
    .then((res) => {
      return res.data.data;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const lensPackagesSlice = createSlice({
  name: "lens-package",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchLensPackages.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchLensPackages.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.lensPackages = payload;
    });
    builder.addCase(fetchLensPackages.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default lensPackagesSlice.reducer;
