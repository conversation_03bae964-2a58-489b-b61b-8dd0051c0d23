"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Eye, EyeOff, MoveRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { NewsLetter } from "@/app/(home)/_components/newsletter";
import { Suspense, useEffect, useState } from "react";
import { redirect, useSearchParams } from "next/navigation";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { resetPassword } from "@/store/slices/auth-slice";
import { ToastErrors } from "@/components/common/toast-errors";

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type RestPasswordFormData = z.infer<typeof resetPasswordSchema>;

function Page() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const params = useSearchParams();

  const token = params.get("token") || "";
  const { error, success, loading } = useSelector(
    (state: RootState) => state.auth
  );

  useEffect(() => {
    if (!token || success) redirect("/auth");
  }, [success, token]);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<RestPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const onSubmit = (data: RestPasswordFormData) => {
    store.dispatch(resetPassword({ token, newPassword: data.password }));
    // handle login here (e.g., call API)
  };

  return (
    <div className="space-y-6">
      <ToastErrors error={error} success={success} />
      <div className="flex items-center justify-center px-4">
        <div
          className={`w-full max-w-md sm:max-w-lg lg:max-w-xl space-y-6 bg-white rounded-md shadow py-8 mt-28`}
        >
          <div className="px-6 sm:px-8">
            <h2 className="text-center text-xl font-semibold mb-3">
              Reset Password
            </h2>

            <p className="text-center text-sm mb-6">
              Your new password must be different from previous used passwords.
            </p>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mb-6">
              <div className="mb-4">
                <label className="block mb-2 text-sm font-medium">
                  Password
                </label>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    {...register("password")}
                    placeholder="••••••••"
                    className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
                  />

                  <Button
                    type="button"
                    size={"icon"}
                    className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
                    onClick={() => setShowPassword((prev) => !prev)}
                  >
                    {showPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="mb-4">
                <label className="block mb-2 text-sm font-medium">
                  Confirm Password
                </label>
                <div className="relative">
                  <Input
                    type={showConfirm ? "text" : "password"}
                    {...register("confirmPassword")}
                    placeholder="••••••••"
                    className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
                  />

                  <Button
                    type="button"
                    size={"icon"}
                    className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
                    onClick={() => setShowConfirm((prev) => !prev)}
                  >
                    {showConfirm ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              <Button
                disabled={isSubmitting}
                size={"lg"}
                className="w-full py-4 font-semibold uppercase mb-6"
              >
                {isSubmitting || loading ? (
                  "Loading..."
                ) : (
                  <>
                    Reset Password <MoveRight />{" "}
                  </>
                )}
              </Button>
            </form>

            <p className="mb-2 text-sm">
              Already have an account?{" "}
              <Link className="text-blue-400 font-semibold ml-1" href={"/auth"}>
                Sign In
              </Link>
            </p>

            <p className="mb-2 text-sm">
              Don't have an account?{" "}
              <Link
                className="text-blue-400 font-semibold ml-1"
                href={"/auth?t=r"}
              >
                Sign Up
              </Link>
            </p>

            <Separator />

            <p className="text-sm">
              You may contact{" "}
              <Link href={"/help"} className="text-primary">
                Customer Service
              </Link>{" "}
              for help restoring access to your account.
            </p>
          </div>
        </div>
      </div>

      <NewsLetter />
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    // You could have a loading skeleton as the `fallback` too
    <Suspense>
      <Page />
    </Suspense>
  );
}
