// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { ILensCategorySlice, ILensCategory } from "@/interfaces";

// CONSTANTS
import { LENS_CATEGORY_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: ILensCategorySlice = {
  lensCategories: [],
  loading: true,
  error: "",
};

export const fetchLensCategories = createAsyncThunk<
  ILensCategory[],
  void,
  FetchDataRejected
>("lensCategories/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(LENS_CATEGORY_URL)
    .then((res) => {
      return res.data.data;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const lensCategoriesSlice = createSlice({
  name: "lens-category",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchLensCategories.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchLensCategories.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.lensCategories = payload;
    });
    builder.addCase(fetchLensCategories.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default lensCategoriesSlice.reducer;
