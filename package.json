{"name": "ecom-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/face_mesh": "^0.4.1633559619", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-slot": "^1.2.2", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.6.1", "@tensorflow-models/face-landmarks-detection": "^0.0.3", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "face-api.js": "^0.22.2", "libphonenumber-js": "^1.12.6", "lucide-react": "^0.476.0", "next": "15.1.7", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-webcam": "^7.2.0", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "three": "^0.174.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.174.0", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}