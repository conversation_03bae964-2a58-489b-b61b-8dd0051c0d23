// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IBrand, IBrandSlice } from "@/interfaces";

// ERROR HANDLER

// CONSTANTS
import { BRAND_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: IBrandSlice = {
  brands: [],
  loading: true,
  error: "",
};

export const fetchBrands = createAsyncThunk<IBrand[], void, FetchDataRejected>(
  "brands/fetch",
  async (_, { rejectWithValue }) => {
    return api_interceptor
      .get(BRAND_URL)
      .then((res) => {
        return res.data.data;
      })
      .catch((err) => {
        return rejectWithValue({ message: err.message || err });
      });
  }
);

const brandsSlice = createSlice({
  name: "brand",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchBrands.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchBrands.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.brands = payload;
    });
    builder.addCase(fetchBrands.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default brandsSlice.reducer;
