import { cn } from "@/lib/utils";
import React from "react";

interface WrapperProps {
  children: React.ReactNode;
  vertical?: boolean;
  className?: string;
}

export const Wrapper: React.FC<WrapperProps> = ({
  children,
  className = "",
  vertical = false,
}) => {
  return (
    <div
      className={cn(
        "px-3 sm:px-6 lg:px-10",
        vertical && "py-3 sm:py-6 lg:py-10",
        className
      )}
    >
      {children}
    </div>
  );
};
