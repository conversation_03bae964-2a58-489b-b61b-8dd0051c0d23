"use client";

import { Fragment, useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogBackdrop,
  DialogPanel,
  Popover,
  PopoverButton,
  PopoverGroup,
  PopoverPanel,
  Tab,
  TabGroup,
  TabList,
  TabPanel,
  TabPanels,
} from "@headlessui/react";
import {
  Bars3Icon,
  ShoppingBagIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { Heart, Search, User } from "lucide-react";
// import { SearchHover } from "./search-hover";
import { AccountHover } from "./account-hover";
import { WishlistHover } from "./wishlist-hover";
import { CartHover } from "./cart-hover";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "../ui/hover-card";
import { Wrapper } from "./wrapper";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import store from "@/store/store";
import { fetchEyeWears } from "@/store/slices/eye-wear-slice";
import { fetchFrameShapes } from "@/store/slices/frame-shape-slice";

export const Navbar = () => {
  const {
    user: { user },
    eyeWear: { eyeWears },
    frameShape: { frameShapes },
  } = useSelector((state: RootState) => state);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (!eyeWears.length) {
      store.dispatch(fetchEyeWears());
    }
    if (!frameShapes.length) {
      store.dispatch(fetchFrameShapes());
    }
  }, [eyeWears.length, frameShapes.length]);

  const navigation = {
    categories: [
      {
        id: "eye-wear",
        name: "Eye Wear",
        sections: eyeWears.map((eyeWear) => ({
          name: eyeWear.name,
          href: `/product?eyeWear=${eyeWear.slug}`,
        })),
      },
      {
        id: "frame-shape",
        name: "Frame Shape",
        sections: frameShapes.map((frameShape) => ({
          name: frameShape.name,
          href: `/product?frameShape=${frameShape.slug}`,
        })),
      },
      // {
      //   id: "women",
      //   name: "Women",
      //   sections: [
      //     {
      //       id: "eyeglasses",
      //       name: "Eyeglasses",
      //       items: [
      //         { name: "Round Frames", href: "#" },
      //         { name: "Cat-Eye Frames", href: "#" },
      //         { name: "Square Frames", href: "#" },
      //         { name: "Rimless Glasses", href: "#" },
      //         { name: "Metal Frames", href: "#" },
      //         { name: "Plastic Frames", href: "#" },
      //         { name: "Browse All", href: "#" },
      //       ],
      //     },
      //     {
      //       id: "sunglasses",
      //       name: "Sunglasses",
      //       items: [
      //         { name: "Aviator", href: "#" },
      //         { name: "Oversized", href: "#" },
      //         { name: "Wayfarer", href: "#" },
      //         { name: "Round", href: "#" },
      //         { name: "Sport Sunglasses", href: "#" },
      //         { name: "Polarized", href: "#" },
      //       ],
      //     },
      //     {
      //       id: "brands",
      //       name: "Brands",
      //       items: [
      //         { name: "Ray-Ban", href: "#" },
      //         { name: "Oakley", href: "#" },
      //         { name: "Gucci", href: "#" },
      //         { name: "Prada", href: "#" },
      //         { name: "Versace", href: "#" },
      //       ],
      //     },
      //   ],
      // },
      // {
      //   id: "men",
      //   name: "Men",
      //   sections: [
      //     {
      //       id: "eyeglasses",
      //       name: "Eyeglasses",
      //       items: [
      //         { name: "Rectangle Frames", href: "#" },
      //         { name: "Browline Frames", href: "#" },
      //         { name: "Round Frames", href: "#" },
      //         { name: "Metal Frames", href: "#" },
      //         { name: "Plastic Frames", href: "#" },
      //         { name: "Browse All", href: "#" },
      //       ],
      //     },
      //     {
      //       id: "sunglasses",
      //       name: "Sunglasses",
      //       items: [
      //         { name: "Aviator", href: "#" },
      //         { name: "Wayfarer", href: "#" },
      //         { name: "Wraparound", href: "#" },
      //         { name: "Polarized", href: "#" },
      //         { name: "Sport Sunglasses", href: "#" },
      //       ],
      //     },
      //     {
      //       id: "brands",
      //       name: "Brands",
      //       items: [
      //         { name: "Ray-Ban", href: "#" },
      //         { name: "Oakley", href: "#" },
      //         { name: "Tom Ford", href: "#" },
      //         { name: "Persol", href: "#" },
      //       ],
      //     },
      //   ],
      // },
    ],
    pages: [
      {
        name: "Deal",
        href: "/deal",
      },
      {
        name: "Transition ®",
        href: "/transition-r",
      },
      {
        name: "Reglaze",
        href: "/reglaze",
      },
    ],
  };

  const {
    cart: { totalItems },
    wishlist: { wishlist },
  } = useSelector((state: RootState) => state);

  const rightAction = [
    // {
    //   icon: Search,
    //   href: "/search",
    //   component: SearchHover,
    // },
    {
      icon: User,
      href: user ? "/account" : "/auth",
      component: AccountHover,
      authRequired: true,
    },
    {
      icon: Heart,
      href: user ? "/account/wishlist" : "/auth",
      component: WishlistHover,
      badge: wishlist.length || 0,
      authRequired: true,
    },
    {
      icon: ShoppingBagIcon,
      href: "/account/cart",
      component: CartHover,
      badge: totalItems || 0,
    },
  ];

  return (
    <div>
      {/* Mobile menu */}
      <Dialog open={open} onClose={setOpen} className="relative z-40 lg:hidden">
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-black/25 transition-opacity duration-300 ease-linear data-closed:opacity-0"
        />

        <div className="fixed inset-0 z-40 flex">
          <DialogPanel
            transition
            className="relative flex w-full max-w-xs transform flex-col overflow-y-auto bg-white pb-12 transition duration-300 ease-in-out data-closed:-translate-x-full"
          >
            <div className="flex px-4 pt-5 pb-2">
              <button
                type="button"
                onClick={() => setOpen(false)}
                className="relative -m-2 inline-flex items-center justify-center rounded-md p-2 text-gray-400"
              >
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Close menu</span>
                <XMarkIcon aria-hidden="true" className="size-6" />
              </button>
            </div>

            {/* Links */}
            <TabGroup className="mt-2">
              <div className="">
                <TabList className="-mb-px flex space-x-8 px-4">
                  {navigation.categories.map((category) => (
                    <Tab
                      key={category.name}
                      className="flex-1 px-1 py-4 text-base font-medium whitespace-nowrap text-gray-900"
                    >
                      {category.name}
                    </Tab>
                  ))}
                </TabList>
              </div>

              <TabPanels as={Fragment}>
                {navigation.categories.map((category) => (
                  <TabPanel
                    key={category.name}
                    className="space-y-10 px-4 pt-10 pb-8"
                  >
                    {category.sections.map((section) => (
                      <div key={section.name} className="flow-root">
                        <Link
                          href={section.href}
                          className="-m-2 block p-2 text-gray-500"
                          onClick={() => setOpen(false)}
                        >
                          {section.name}
                        </Link>
                      </div>
                    ))}
                  </TabPanel>
                ))}
              </TabPanels>
            </TabGroup>

            <div className="space-y-6 border-t border-gray-200 px-4 py-6">
              {navigation.pages.map((page: any) => (
                <div key={page.name} className="flow-root">
                  <Link
                    href={page.href}
                    className="-m-2 block p-2 font-medium text-gray-900"
                  >
                    {page.name}
                  </Link>
                </div>
              ))}
            </div>

            <div className="space-y-6 border-t border-gray-200 px-4 py-6">
              <div className="flow-root">
                <a
                  href="#"
                  className="-m-2 block p-2 font-medium text-gray-900"
                >
                  Sign in
                </a>
              </div>
            </div>
          </DialogPanel>
        </div>
      </Dialog>

      <header className="relative bg-white">
        <p className="flex h-10 items-center justify-center bg-primary px-4 text-sm font-medium text-white sm:px-6 lg:px-8">
          Get free delivery on orders over $100
        </p>

        <Wrapper className=" bg-[#F9F6F1]">
          <nav aria-label="Top">
            <div className="flex h-16 items-center justify-between">
              <button
                type="button"
                onClick={() => setOpen(true)}
                className="relative rounded-md bg-white p-2 text-gray-400 lg:hidden"
              >
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open menu</span>
                <Bars3Icon aria-hidden="true" className="size-6" />
              </button>

              {/* Logo */}
              <div className="ml-4 flex lg:ml-0">
                <Link href="/">
                  <span className="sr-only">Quality Fast Specs</span>
                  <img alt="" src="/logo.svg" className="h-8 w-auto" />
                </Link>
              </div>

              {/* Flyout menus */}
              <PopoverGroup className="hidden lg:block">
                <div className="flex h-full space-x-8">
                  {navigation.categories.map((category) => {
                    return (
                      <Popover key={category.name} className="flex">
                        {({ close }) => (
                          <>
                            <div className="relative flex">
                              <PopoverButton className="relative z-20 -mb-px flex items-center border-b-2 border-transparent pt-px text-sm font-medium text-gray-700 transition-colors outline-none duration-200 ease-out hover:text-gray-800 data-open:border-primary data-open:text-primary">
                                {category.name}
                              </PopoverButton>
                            </div>

                            <PopoverPanel
                              transition
                              className="absolute inset-x-0 top-full text-sm text-gray-500 transition data-closed:opacity-0 data-enter:duration-200 data-enter:ease-out data-leave:duration-150 data-leave:ease-in"
                            >
                              <div
                                aria-hidden="true"
                                className="absolute inset-0 top-1/2 bg-white shadow-sm"
                              />

                              <div className="relative bg-white z-10">
                                <div className="w-full px-8 py-8">
                                  <div className="grid grid-cols-7 gap-x-8 gap-y-10 text-sm">
                                    {category.sections.map((section) => (
                                      <div
                                        key={section.name}
                                        className="flow-root"
                                      >
                                        <Link
                                          href={section.href}
                                          className="-m-2 block p-2 text-gray-500"
                                          onClick={() => close()}
                                        >
                                          {section.name}
                                        </Link>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </PopoverPanel>
                          </>
                        )}
                      </Popover>
                    );
                  })}

                  {navigation.pages.map((page: any) => (
                    <Link
                      key={page.name}
                      href={page.href}
                      className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-800"
                    >
                      {page.name}
                    </Link>
                  ))}
                </div>
              </PopoverGroup>

              <div className="flex items-center gap-4">
                {rightAction.map((action, index) => (
                  <HoverCard openDelay={200} key={"hover-card" + index}>
                    <HoverCardTrigger asChild>
                      <Link href={action.href} className="relative">
                        {<action.icon className="text-gray-700 size-5" />}
                        {action.badge ? (
                          <span className="absolute -top-3 -right-3 h-5 w-5 rounded-full bg-primary text-xs flex justify-center items-center text-white">
                            {action.badge}
                          </span>
                        ) : (
                          ""
                        )}
                      </Link>
                    </HoverCardTrigger>

                    {(action.authRequired ? user : true) && (
                      <HoverCardContent className="bg-white border-none">
                        <action.component />
                      </HoverCardContent>
                    )}
                  </HoverCard>
                ))}
              </div>
            </div>
          </nav>
        </Wrapper>
      </header>
    </div>
  );
};
