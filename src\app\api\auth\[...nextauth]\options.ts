import { LOGIN_URL } from "@/constants/url";
import { api_interceptor } from "@/utils/api-interceptor";
import { getErrorMessage } from "@/utils/error-message";
import { NextAuthOptions } from "next-auth";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialProvider({
      id: "credentials",
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req): Promise<any> {
        return credentials;
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async signIn({ user, account }: { user: any; account: any }) {
      try {
        const body = { email: user.username, password: user.password };
        const response = await api_interceptor.post(LOGIN_URL, body);
        const {
          user: { fullName },
          token,
        } = response.data.data;
        user.fullName = fullName;
        user.token = token;
        delete user.password;
        return true;
      } catch (error: any) {
        throw new Error(getErrorMessage(error, "Something went wrong"));
      }
      // return false;
    },
    async jwt({ token, user, account }) {
      return { ...token, ...user };
    },
    async session({ session, token, user, account }: any) {
      session.user.token = token.token;
      session.user.fullName = token.fullName;
      session.user.email = token.username;
      return session;
    },
  },
  pages: {
    error: "/auth",
    newUser: "/auth",
    signIn: "/auth",
    signOut: "/auth",
  },
};
