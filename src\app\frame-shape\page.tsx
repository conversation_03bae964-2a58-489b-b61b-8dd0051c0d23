"use client";
import { useEffect } from "react";
import store, { RootState } from "@/store/store";
import { getImageUrl } from "@/utils/image-url";
import Image from "next/image";
import Link from "next/link";
import { useSelector } from "react-redux";
import { fetchFrameShapes } from "@/store/slices/frame-shape-slice";
import { Wrapper } from "@/components/common/wrapper";

const ShopByFrameShape = () => {
  const { frameShapes, loading } = useSelector(
    (state: RootState) => state.frameShape
  );

  useEffect(() => {
    if (!frameShapes.length) {
      store.dispatch(fetchFrameShapes());
    }
    return () => {};
  }, []);

  return (
    <Wrapper vertical>
      <h3 className="font-semibold text-center text-2xl mb-6">
        Shop By Frame Shape
      </h3>
      <div className="gap-5 flex flex-col lg:flex-row">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-5 w-full">
          {frameShapes.map((shape, index) => (
            <Link
              href={`/product?frameShape=${shape.slug}`}
              key={index}
              className="flex flex-col items-center"
            >
              <Image
                width={100}
                height={100}
                src={getImageUrl(shape.image)}
                alt={shape.name}
                className="h-12 object-contain"
              />
              <p>{shape.name}</p>
            </Link>
          ))}
        </div>
      </div>
    </Wrapper>
  );
};

export default ShopByFrameShape;
