"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface Column<T> {
  header: string;
  accessor: keyof T;
  className?: string;
  render?: (value: any, row: T) => React.ReactNode;
}

interface TableProps<T> {
  columns: Column<T>[];
  data: T[];
  className?: string;
  tableClassName?: string;
  headerClassName?: string;
  rowClassName?: string;
  bodyClassName?: string;
  striped?: boolean;
  emptyMessage?: string;
}

export function Table<T>({
  columns,
  data,
  className,
  tableClassName,
  headerClassName,
  rowClassName,
  bodyClassName,
  striped = false,
  emptyMessage = "No data available.",
}: TableProps<T>) {
  return (
    <div className={cn("w-full overflow-x-auto rounded-md border", className)}>
      <table className={cn("min-w-full divide-y text-sm", tableClassName)}>
        <thead className="bg-gray-100">
          <tr>
            {columns.map((col, index) => (
              <th
                key={index}
                className={cn(
                  "px-4 py-2 text-left font-semibold text-gray-600 whitespace-nowrap",
                  col.className,
                  headerClassName
                )}
              >
                {col.header}
              </th>
            ))}
          </tr>
        </thead>

        <tbody className={cn("divide-y", bodyClassName)}>
          {data.length === 0 ? (
            <tr>
              <td
                colSpan={columns.length}
                className="px-4 py-4 text-center text-gray-400"
              >
                {emptyMessage}
              </td>
            </tr>
          ) : (
            data.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={cn(
                  striped && rowIndex % 2 === 1 ? "bg-gray-50" : "",
                  rowClassName
                )}
              >
                {columns.map((col, colIndex) => (
                  <td
                    key={colIndex}
                    className={cn("px-4 py-2 whitespace-nowrap", col.className)}
                  >
                    {col.render
                      ? col.render(row[col.accessor], row)
                      : String(row[col.accessor])}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}
