// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IEyeWear, IEyeWearSlice } from "@/interfaces";

// CONSTANTS
import { EYE_WEAR_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: IEyeWearSlice = {
  eyeWears: [],
  loading: true,
  error: "",
};

export const fetchEyeWears = createAsyncThunk<
  IEyeWear[],
  void,
  FetchDataRejected
>("eyeWear/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(EYE_WEAR_URL + `?page=1&limit=100`)
    .then((res) => {
      return res.data.data.data;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const eyeWearSlice = createSlice({
  name: "eyeWear",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchEyeWears.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchEyeWears.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.eyeWears = payload;
    });
    builder.addCase(fetchEyeWears.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default eyeWearSlice.reducer;
