"use client";
import React, { useState, useEffect } from "react";
import { PRODUCT_URL } from "@/constants/url";
import { IProduct } from "@/interfaces";
import { api_interceptor } from "@/utils/api-interceptor";
import { Product } from "@/components/common/product";

interface RelatedProductProps {
  product: IProduct;
}

export const RelatedProducts: React.FC<RelatedProductProps> = ({ product }) => {
  const [products, setProducts] = useState<IProduct[]>([]);
  useEffect(() => {
    const query = `?excludedProducts=${product._id}&categoryId=${product.category}`;
    api_interceptor
      .get(PRODUCT_URL + query)
      .then((res) => setProducts(res.data.data.products));
    return () => {};
  }, []);

  if (!products.length) return; // if no products, return nothing

  return (
    <div className="mt-4">
      <p className="mb-2 font-medium text-xl">Related Products</p>
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {products.map((prod) => (
          <Product product={prod} key={prod._id} />
        ))}
      </div>
    </div>
  );
};
