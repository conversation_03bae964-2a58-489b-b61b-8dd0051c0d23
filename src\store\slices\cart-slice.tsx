// REDUX
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

// INTERFACE
import { ICartItem, ICartSlice, ICoupon, ICouponResponse } from "@/interfaces";
import { STORAGE_KEY } from "@/constants/keys";
import { getCartId } from "@/utils/generate-cart-id";
import { COUPON_URL } from "@/constants/url";
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";
import { getErrorMessage } from "@/utils/error-message";

// Create async thunk for applying coupon
export const applyCoupon = createAsyncThunk<
  ICouponResponse,
  string,
  FetchDataRejected
>("cart/applyCoupon", async (code, { rejectWithValue }) => {
  return api_interceptor
    .get(`${COUPON_URL}/code/${code}`)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Invalid coupon code"),
      });
    });
});

const initialState: ICartSlice = {
  cart: [],
  subtotal: 0,
  totalAmount: 0,
  totalItems: 0,
  couponDiscount: 0,
  error: "",
};

const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    revalidateCoupon: (state) => {
      const coupon = state.coupon;

      if (!coupon) return;

      const subtotal = state.subtotal;
      if (
        subtotal < +(coupon.minPurchaseAmount || 0) &&
        coupon.couponType === "normal"
      ) {
        state.error = "Coupon is not applicable for this order amount";
        state.coupon = undefined;
        state.couponDiscount = 0;
        localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
        return;
      }

      // Calculate discount based on coupon type
      let discount = 0;

      if (coupon.couponType === "range") {
        state.cart.forEach((cart) => {
          const totalPrice = cart.lensData?.totalPrice || 0;
          const availableRange = coupon.ranges.find(
            (range) => +range.start <= +totalPrice && +totalPrice >= +range.end
          );
          if (availableRange) {
            if (availableRange.discountType === "percentage") {
              discount += (totalPrice * availableRange.discountValue) / 100;
              // Apply max discount if specified
            } else {
              // fixed discount
              discount += availableRange.discountValue;
            }
          } else if (coupon.lastRange?.apply) {
            if (coupon.lastRange.discountType === "percentage") {
              discount += (totalPrice * coupon.lastRange.discountValue) / 100;
            } else {
              // fixed discount
              discount += coupon.lastRange.discountValue;
            }
          }
        });
      } else {
        if (coupon.discountType === "percentage") {
          discount = (subtotal * coupon.discountValue) / 100;
          // Apply max discount if specified
          if (coupon.maxDiscount && discount > coupon.maxDiscount) {
            discount = coupon.maxDiscount;
          }
        } else {
          // fixed discount
          discount = coupon.discountValue;
        }
      }

      state.coupon = coupon;
      state.couponDiscount = discount;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    },
    addToCart: (state, action: PayloadAction<Omit<ICartItem, "id">>) => {
      const { product, quantity, variation, lensData } = action.payload;

      // If lens data is present, create a unique ID for this specific lens configuration
      const lensConfigId = lensData
        ? `-lens-${lensData.visionType}-${lensData.lensCategory}-${lensData.lensPackage.name}`
        : "";
      const cartId = getCartId(
        product._id,
        (variation?._id || "") + lensConfigId
      );

      const existingItem = state.cart.find((item) => item.id === cartId);

      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        state.cart.push({ product, quantity, variation, lensData, id: cartId });
      }

      // Calculate price including lens package if present
      const basePrice = variation?.price || product.basePrice;
      const lensPrice = lensData?.lensPackage?.price || 0;
      const totalItemPrice = basePrice + lensPrice;

      state.totalAmount += totalItemPrice * quantity;
      state.subtotal += totalItemPrice * quantity;
      state.totalItems += quantity;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    },
    removeFromCart: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const item = state.cart.find((item) => item.id === id);

      if (item) {
        // Calculate price including lens package if present
        const basePrice = item.variation?.price || item.product.basePrice;
        const lensPrice = item.lensData?.lensPackage?.price || 0;
        const totalItemPrice = basePrice + lensPrice;

        state.totalAmount -= totalItemPrice * item.quantity;
        state.subtotal -= totalItemPrice * item.quantity;
        state.totalItems -= item.quantity;
        state.cart = state.cart.filter((item) => item.id !== id);
        localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
      }
    },
    updateQuantity: (
      state,
      action: PayloadAction<{ id: string; quantity: number }>
    ) => {
      const { id, quantity } = action.payload;
      const item = state.cart.find((item) => item.id === id);

      if (item) {
        // Calculate price including lens package if present
        const basePrice = item.variation?.price || item.product.basePrice;
        const lensPrice = item.lensData?.lensPackage?.price || 0;
        const totalItemPrice = basePrice + lensPrice;

        state.totalAmount += totalItemPrice * (quantity - item.quantity);

        state.totalItems += quantity - item.quantity;
        item.quantity = quantity;
        localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
      }
    },
    clearCart: (state) => {
      state.cart = [];
      state.totalAmount = 0;
      state.subtotal = 0;
      state.totalItems = 0;
      state.coupon = undefined;
      state.couponDiscount = 0;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    },
    fetchCart: (state) => {
      const cart = localStorage.getItem(STORAGE_KEY);
      if (cart) {
        const parsedCart = JSON.parse(cart);
        // Recalculate total amount to ensure it's correct
        let totalAmount = 0;
        let totalItems = 0;

        parsedCart.cart.forEach((item: ICartItem) => {
          const basePrice = item.variation?.price || item.product.basePrice;
          const lensPrice = item.lensData?.lensPackage?.price || 0;
          const totalItemPrice = basePrice + lensPrice;

          totalAmount += totalItemPrice * item.quantity;
          totalItems += item.quantity;
        });

        parsedCart.totalAmount = totalAmount;
        parsedCart.subtotal = totalAmount;
        parsedCart.totalItems = totalItems;

        // Recalculate coupon discount if a coupon is applied
        if (parsedCart.coupon) {
          let discount = 0;

          if (parsedCart.coupon.discountType === "percentage") {
            discount = (totalAmount * parsedCart.coupon.discountValue) / 100;
            // Apply max discount if specified
            if (
              parsedCart.coupon.maxDiscountAmount &&
              discount > parsedCart.coupon.maxDiscountAmount
            ) {
              discount = parsedCart.coupon.maxDiscountAmount;
            }
          } else {
            // fixed discount
            discount = parsedCart.coupon.discountValue;
          }

          parsedCart.couponDiscount = discount;
        }

        return parsedCart;
      }
      return initialState;
    },
    removeCoupon: (state) => {
      state.coupon = undefined;
      state.couponDiscount = 0;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    },
  },
  extraReducers: (builder) => {
    builder.addCase(applyCoupon.fulfilled, (state, action) => {
      const coupon = action.payload.data;
      const subtotal = state.subtotal;

      if (
        subtotal < +(coupon.minPurchaseAmount || 0) &&
        coupon.couponType === "normal"
      ) {
        state.error = "Coupon is not applicable for this order amount";
        return;
      }

      // Calculate discount based on coupon type
      let discount = 0;

      if (coupon.couponType === "range") {
        state.cart.forEach((cart) => {
          const totalPrice = cart.lensData?.totalPrice || 0;
          const availableRange = coupon.ranges.find(
            (range) => +range.start <= +totalPrice && +totalPrice >= +range.end
          );
          if (availableRange) {
            if (availableRange.discountType === "percentage") {
              discount += (totalPrice * availableRange.discountValue) / 100;
              // Apply max discount if specified
            } else {
              // fixed discount
              discount += availableRange.discountValue;
            }
          } else if (coupon.lastRange?.apply) {
            if (coupon.lastRange.discountType === "percentage") {
              discount += (totalPrice * coupon.lastRange.discountValue) / 100;
            } else {
              // fixed discount
              discount += coupon.lastRange.discountValue;
            }
          }
        });
      } else {
        if (coupon.discountType === "percentage") {
          discount = (subtotal * coupon.discountValue) / 100;
          // Apply max discount if specified
          if (coupon.maxDiscount && discount > coupon.maxDiscount) {
            discount = coupon.maxDiscount;
          }
        } else {
          // fixed discount
          discount = coupon.discountValue;
        }
      }

      state.coupon = coupon;
      state.couponDiscount = discount;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    });
  },
});

export const {
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  fetchCart,
  removeCoupon,
  revalidateCoupon,
} = cartSlice.actions;

export default cartSlice.reducer;
