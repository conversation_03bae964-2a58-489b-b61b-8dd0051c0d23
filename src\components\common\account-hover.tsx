"use client";

import Link from "next/link";
import React from "react";
import { Button } from "../ui/button";
import { signOut } from "next-auth/react";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";

export const AccountHover = () => {
  const { user } = useSelector((state: RootState) => state.user);

  const links = [
    {
      label: "My Account",
      href: "/account",
    },
    {
      label: "My Orders",
      href: "/account/orders",
    },
    {
      label: "My Details",
      href: "/account/setting",
    },
    {
      label: "My Addresses",
      href: "/account/address",
    },
    {
      label: "Sign Out",
      type: "LOGOUT",
    },
  ];

  const callAction = (link: any) => {
    switch (link.type) {
      case "LOGOUT":
        signOut({ callbackUrl: "/" });
        break;
    }
  };

  return (
    <div>
      <h2 className="font-semibold uppercase text-center border-b border-gray-200 mb-4">
        Hi, {user?.fullName}
      </h2>

      <div className="w-52 pt-4">
        {links?.map((link, index) => {
          return (
            <div className="text-center" key={index}>
              <Button
                onClick={() => (link.href ? "" : callAction(link))}
                className="w-6 bg-white shadow-none border-none text-text-accent hover:bg-white hover:text-text-accent/5"
              >
                {link.href ? (
                  <Link href={link.href}>{link.label}</Link>
                ) : (
                  link.label
                )}
              </Button>
            </div>
          );
        })}
      </div>
    </div>
  );
};
