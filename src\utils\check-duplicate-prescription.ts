import { PrescriptionFormData } from "@/interfaces/prescription";

export const isDuplicatePrescription = (
  newPrescription: PrescriptionFormData,
  existingPrescriptions: PrescriptionFormData[]
): boolean => {
  return existingPrescriptions.some((prescription) => {
    return (
      prescription.name === newPrescription.name ||
      (prescription.rightEye.sph === newPrescription.rightEye.sph &&
        prescription.rightEye.cyl === newPrescription.rightEye.cyl &&
        prescription.rightEye.axis === newPrescription.rightEye.axis &&
        prescription.rightEye.add === newPrescription.rightEye.add &&
        prescription.leftEye.sph === newPrescription.leftEye.sph &&
        prescription.leftEye.cyl === newPrescription.leftEye.cyl &&
        prescription.leftEye.axis === newPrescription.leftEye.axis &&
        prescription.leftEye.add === newPrescription.leftEye.add &&
        prescription.pd === newPrescription.pd)
    );
  });
};
