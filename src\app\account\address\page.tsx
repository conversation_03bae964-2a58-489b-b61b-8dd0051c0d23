"use client";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { fetchAddresses } from "@/store/slices/address-slice";
import store, { RootState } from "@/store/store";
import { Plus } from "lucide-react";
import Link from "next/link";
import React from "react";
import { useSelector } from "react-redux";

const Page = () => {
  const {
    address: { address },
    user: { user },
  } = useSelector((state: RootState) => state);

  const removeAddress = (address: any) => {
    console.log(address);
  };

  useEffect(() => {
    if (user) {
      store.dispatch(fetchAddresses());
    }
    return () => {};
  }, [user]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <Link href={"/account/address/add-edit"}>
        <div className="border rounded-sm border-black border-dotted flex justify-center items-center h-full p-5">
          <div className="flex flex-col items-center text-gray-500">
            <Plus size={40} className=" mb-2" />
            <p className="font-semibold">Add Address</p>
          </div>
        </div>
      </Link>

      {address?.map((address, index) => {
        return (
          <div
            key={"address-" + index}
            className="border rounded-sm border-gray-200"
          >
            <div
              className={`${
                address.isDefault ? "border-b border-gray-200" : ""
              }`}
            >
              {address.isDefault ? (
                <p className="text-center py-4 text-gray-400">
                  Default Address
                </p>
              ) : (
                <p className="text-center px-20 py-4 text-gray-400">&nbsp;</p>
              )}
            </div>

            <div className="px-5 py-3 space-y-3">
              <p className="font-semibold">{address?.fullName}</p>
              <p>{address?.address}</p>
              <p>
                {address.city}, {address.state} {address.postalCode}
              </p>
              <p>{address.country}</p>
              <p>Phone Number: {address?.phone}</p>
            </div>

            <div className="px-5 mb-5 flex font-semibold text-sm items-center">
              <Link
                className="text-primary border-none shadow-none bg-transparent p-0 hover:bg-transparent font-semibold"
                href={`/account/address/add-edit?id=${address._id}`}
              >
                Edit
              </Link>
              <span className="text-primary mx-2">|</span>
              <Button
                className="text-primary border-none shadow-none bg-transparent p-0 hover:bg-transparent font-semibold"
                onClick={() => removeAddress(address)}
              >
                Remove
              </Button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Page;
