// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { ICategory, ICategorySlice } from "@/interfaces";

// CONSTANTS
import { CATEGORY_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: ICategorySlice = {
  categories: [],
  loading: true,
  error: "",
};

export const fetchCategories = createAsyncThunk<
  ICategory[],
  void,
  FetchDataRejected
>("categories/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(CATEGORY_URL)
    .then((res) => {
      return res.data.data.data;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const categoriesSlice = createSlice({
  name: "category",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchCategories.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchCategories.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.categories = payload;
    });
    builder.addCase(fetchCategories.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default categoriesSlice.reducer;
