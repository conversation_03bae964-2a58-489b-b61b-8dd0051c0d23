"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ProgressBar } from "@/components/ui/progress-bar";
import { Table } from "@/components/ui/table";
import { IOrder } from "@/interfaces";
import { fetchOrderById } from "@/store/slices/order-slice";
import store, { RootState } from "@/store/store";
import { api_interceptor } from "@/utils/api-interceptor";
import { formatPrice } from "@/utils/format-price";
import { getImageUrl } from "@/utils/image-url";
import {
  Dot,
  Handshake,
  MoveLeft,
  NotebookText,
  Package,
  Plus,
  Truck,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";

const Page = () => {
  const {
    user: { user },
    order: { currentOrder },
  } = useSelector((state: RootState) => state);
  const params = useParams();

  useEffect(() => {
    if (user) {
      store.dispatch(fetchOrderById(params.orderId as any));
    }
    return () => {};
  }, [user]);

  const steps = [
    { label: "Order Placed", icon: <NotebookText size={22} /> },
    { label: "Shipped", icon: <Truck size={22} /> },
    { label: "Delivered", icon: <Handshake size={22} /> },
  ];

  const columns: any = [
    {
      header: "Products",
      render: (_: any, row: any) => productNameRender(row),
      className: "px-6",
    },
    {
      header: "Price",
      render: (_: any, row: any) => priceRender(row),
      className: "text-center px-6",
    },
    {
      header: "Quantity",
      render: (_: any, row: any) => quantityRender(row),
      className: "text-center px-6",
    },
    {
      header: "Sub-Total",
      render: (_: any, row: any) => subTotalRender(row),
      className: "text-center px-6",
    },
  ];

  const productNameRender = (row: any) => {
    const { product, variation } = row;
    return (
      <div className="flex py-6 items-center">
        <Image
          src={getImageUrl(variation.thumbnail || product.thumbnail)}
          width={200}
          height={200}
          alt=""
          className="w-16 mr-4"
        />
        <Link
          href={`/product/${product.slug}`}
          className="text-sm font-semibold text-primary"
        >
          {product.name}
        </Link>
      </div>
    );
  };

  const priceRender = (row: any) => formatPrice({ price: row.price });

  const quantityRender = (row: any) => `x${row.quantity}`;

  const subTotalRender = (row: any) => (
    <div className="font-semibold">
      {formatPrice({ price: row.price * row.quantity })}
    </div>
  );

  const getCurrentStep = () => {
    let step = 0;

    switch (currentOrder?.status) {
      case "pending":
      case "confirmed":
      case "PENDING":
      case "CONFIRMED":
        step = 0;
        break;
      case "shipped":
      case "SHIPPED":
        step = 1;
        break;
      case "delivered":
      case "DELIVERED":
        step = 2;
    }
    return step;
  };

  if (!currentOrder) return null;

  return (
    <div className="border border-gray-200">
      <div className="border-b border-gray-200 flex justify-between items-center py-2 px-5">
        <div className="text-black uppercase flex text-sm items-center font-semibold">
          <Link href="/account/orders" className="mr-3">
            <MoveLeft />
          </Link>
          Order Details
        </div>

        <Button className="bg-transparent border-none shadow-none p-0 text-primary hover:bg-transparent">
          Leave Rating
          <Plus />
        </Button>
      </div>

      <div className="p-6 border-b border-gray-200">
        <div className="border border-primary rounded-sm bg-primary/20 flex justify-between items-center p-6">
          <div className="space-y-2">
            <p className="text-xl font-semibold">#{currentOrder.orderId}</p>

            <p className="flex flex-col lg:flex-row text-sm text-gray-600">
              {currentOrder.items.length} Product
              {currentOrder.items.length > 1 ? "s" : ""}{" "}
              <Dot className="hidden lg:block" />{" "}
              <span>
                Order Placed on{" "}
                {new Date(
                  currentOrder?.createdAt || currentOrder?.date
                ).toLocaleDateString()}
              </span>
            </p>
          </div>

          <div>
            <p className="font-semibold text-2xl text-primary">
              {formatPrice({ price: currentOrder.totalAmount })}
            </p>
          </div>
        </div>

        <p className="py-6">
          Order expected arrival{" "}
          <span className="font-semibold">{currentOrder.delivery}</span>
        </p>

        <ProgressBar steps={steps} currentStep={getCurrentStep()} />
      </div>

      {/* ACTIVITY */}
      {/* <div className="py-8 px-6">
        <h2 className="font-semibold">Order Activity</h2>

        <div className="space-y-4">
          <div className="flex"></div>
        </div>
      </div> */}

      {/* ITEMS */}

      <div className="">
        <h2 className="py-8 px-6 font-semibold">
          Products{" "}
          <span className="text-gray-400">({currentOrder.items.length})</span>
        </h2>

        <div className="">
          <Table
            columns={columns}
            data={currentOrder.items}
            className="border-gray-200 rounded-none border-l-0 border-r-0 w-auto"
            bodyClassName="border-gray-200"
            rowClassName="border-gray-200"
            tableClassName="w-max"
          />
        </div>

        <div className="grid grid-cols-3 gap-8 px-6 py-8">
          {/* BILLING ADDRESS */}
          <div className="border-r border-gray-200">
            <h2 className="font-semibold mb-6">Billing address</h2>

            <div className="space-y-2 text-text-accent">
              <p className="font-semibold text-black">
                {currentOrder?.billingAddress?.fullName}
              </p>
              <p>{currentOrder?.billingAddress.address}</p>
              <p>
                {currentOrder?.billingAddress.city},{" "}
                {currentOrder?.billingAddress?.state}{" "}
                {currentOrder?.billingAddress.postalCode}
              </p>
              <p>{currentOrder?.billingAddress?.country}</p>
              <p>
                <span className="text-black">Phone Number:</span>{" "}
                {currentOrder?.billingAddress?.phone}
              </p>
            </div>
          </div>

          {/* SHIPPING ADDRESS */}
          <div className="border-r border-gray-200">
            <h2 className="font-semibold mb-6">Shipping Address</h2>

            <div className="space-y-2 text-text-accent">
              <p className="font-semibold text-black">
                {currentOrder?.shippingAddress?.fullName}
              </p>
              <p>{currentOrder?.shippingAddress?.address}</p>
              <p>
                {currentOrder?.shippingAddress.city},{" "}
                {currentOrder?.shippingAddress.state}{" "}
                {currentOrder?.shippingAddress.postalCode}
              </p>
              <p>{currentOrder?.shippingAddress.country}</p>
              <p>
                <span className="text-black">Phone Number:</span>{" "}
                {currentOrder?.shippingAddress?.phone}
              </p>
            </div>
          </div>

          {/* ORDER NOTES */}
          <div className="">
            <h2 className="font-semibold mb-6">Order Notes</h2>

            <p className="text-text-accent">{currentOrder.note}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
