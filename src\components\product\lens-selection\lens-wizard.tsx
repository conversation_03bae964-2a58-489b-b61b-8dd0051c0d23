"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { VisionTypeStep } from "./steps/vision-type-step";
import { PrescriptionStep } from "./steps/prescription-step";
import { LensCategoryStep } from "./steps/lens-category-step";
import { LensPackageStep } from "./steps/lens-package-step";
import { ReviewStep } from "./steps/review-step";
import { IProduct } from "@/interfaces/product";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { fetchLensCategories } from "@/store/slices/lens-category-slice";
import { fetchLensPackages } from "@/store/slices/lens-package-slice";
import { IVariation } from "@/interfaces/variations";
import { PrismData } from "@/components/common/more-option";

export interface LensSelectionData {
  visionType: string;
  packageId: string;
  packagePrice: number;
  prescriptionType: string;
  prescription?: {
    name?: string;
    pd?: string;
    rightEye?: {
      sph: string;
      cyl: string;
      axis: string;
      add: string;
    };
    leftEye?: {
      sph: string;
      cyl: string;
      axis: string;
      add: string;
    };
  };
  lensCategory: string;
  lensPackage: {
    name: string;
    price: number;
    _id: string;
    subPackage?: { title: string; price: number } | null;
  };
  totalPrice: number;
  prismSelected?: boolean;
  prismData?: PrismData;
}

interface LensWizardProps {
  isOpen: boolean;
  onClose: () => void;
  product: IProduct;
  variation: IVariation;
  onAddToCart: (product: IProduct, lensData: LensSelectionData) => void;
}

export const LensWizard: React.FC<LensWizardProps> = ({
  isOpen,
  onClose,
  product,
  onAddToCart,
  variation,
}) => {
  const { lensCategories } = useSelector(
    (state: RootState) => state.lensCategory
  );
  const { lensPackages } = useSelector((state: RootState) => state.lensPackage);

  const [currentStep, setCurrentStep] = useState(1);
  const [lensData, setLensData] = useState<LensSelectionData>({
    visionType: "",
    packageId: "",
    packagePrice: 0,
    prescriptionType: "",
    lensCategory: "",
    lensPackage: {
      name: "",
      price: 0,
      _id: "",
      subPackage: null,
    },
    totalPrice: variation.price || product.basePrice,
    prescription: {},
  });

  const handleNext = (_lensData?: LensSelectionData) => {
    // Skip prescription step if Non Prescription is selected
    if (
      currentStep === 1 &&
      (_lensData?.visionType || lensData.visionType) === "non-prescription"
    ) {
      setCurrentStep(3); // Skip to lens category step
    } else {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    // Handle going back when prescription step was skipped
    if (currentStep === 3 && lensData.visionType === "non-prescription") {
      setCurrentStep(1); // Go back to vision type step
    } else {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const updateLensData = (data: Partial<LensSelectionData>, next?: boolean) => {
    const updatedData = { ...lensData, ...data };

    const prismPrice = updatedData.prismSelected ? 15 : 0;

    updatedData.totalPrice =
      (variation.price || product.basePrice) +
      (updatedData.lensPackage?.price || 0) +
      updatedData.packagePrice +
      (updatedData.lensPackage.subPackage?.price || 0) +
      prismPrice;
    setLensData(updatedData);

    if (next) {
      handleNext(updatedData);
    }
  };

  const handleAddToCart = () => {
    onAddToCart(product, lensData);
    onClose();
  };

  useEffect(() => {
    if (!lensCategories.length) {
      store.dispatch(fetchLensCategories());
    }

    if (!lensPackages.length) {
      store.dispatch(fetchLensPackages());
    }
    return () => {};
  }, []);

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <VisionTypeStep
            lensData={lensData}
            updateLensData={updateLensData}
            onNext={handleNext}
          />
        );
      case 2:
        return (
          <PrescriptionStep
            lensData={lensData}
            updateLensData={updateLensData}
            onNext={handleNext}
            onBack={handleBack}
            operation="add"
          />
        );
      case 3:
        return (
          <LensCategoryStep
            lensData={lensData}
            updateLensData={updateLensData}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 4:
        return (
          <LensPackageStep
            lensData={lensData}
            updateLensData={updateLensData}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 5:
        return (
          <ReviewStep
            lensData={lensData}
            updateLensData={updateLensData}
            product={product}
            variation={variation}
            onBack={handleBack}
            onAddToCart={handleAddToCart}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Select Your Lenses</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          {/* <div className="flex justify-between mb-6">
            {(lensData.visionType === "non-prescription"
              ? [1, null, 3, 4, 5] // Skip step 2 for non-prescription
              : [1, 2, 3, 4, 5]
            )
              .map((step, index) => {
                if (step === null) return null; // Don't render skipped steps

                // Calculate the visual step number (for display)
                const displayStep = index + 1;

                // Determine if this step should be active
                const isActive = step <= currentStep;

                return (
                  <div
                    key={displayStep}
                    className={`flex flex-col items-center ${
                      isActive ? "text-primary" : "text-gray-400"
                    }`}
                  >
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                        isActive
                          ? "bg-primary text-white"
                          : "bg-gray-200 text-gray-500"
                      }`}
                    >
                      {displayStep}
                    </div>
                    <span className="text-xs">
                      {step === 1
                        ? "Vision Type"
                        : step === 2
                        ? "Prescription"
                        : step === 3
                        ? "Lens Category"
                        : step === 4
                        ? "Lens Package"
                        : "Review"}
                    </span>
                  </div>
                );
              })
              .filter(Boolean)}
          </div> */}
          {renderStep()}
        </div>
      </DialogContent>
    </Dialog>
  );
};
