"use client";
import React, { useState, useEffect } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { Check, Info, LucideIcon } from "lucide-react";
import { formatPrice } from "@/utils/format-price";
import { ILensPackage, ILensSubPackage } from "@/interfaces";
import Image from "next/image";
import { getImageUrl } from "@/utils/image-url";
import { Popover, PopoverTrigger } from "@/components/ui/popover";
import { PopoverContent } from "@radix-ui/react-popover";

interface LensesPackageProps {
  data: ILensPackage;
  selected: boolean;
  packageId: string;
  subPackage?: string | null;
  handleSelect: (
    newPackage: ILensPackage,
    subPackage?: ILensSubPackage | null
  ) => void;
}

export const LensesPackage: React.FC<LensesPackageProps> = ({
  data,
  selected,
  handleSelect,
  packageId,
  subPackage,
}) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  const handleAccordionSelect = () => {
    if (data.subPackages?.length) {
      setExpanded(!expanded);
      return;
    }
    handleSelect(data, null);
  };

  const handleAccordionItemSelect = (newSubPackage: ILensSubPackage) => {
    handleSelect(data, newSubPackage);
  };

  useEffect(() => {
    if (data._id === packageId) {
      setExpanded(true);
    }
    return () => {};
  }, []);

  return (
    <div>
      <div
        key={data._id}
        className={cn(
          "p-4 border rounded-md cursor-pointer border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-all",
          selected && "border-primary bg-primary/5"
        )}
        onClick={handleAccordionSelect}
      >
        <div className="flex items-start gap-4">
          <Image
            src={getImageUrl(data.image)}
            alt={data.name}
            width={200}
            height={200}
            className="w-[50px] h-[50px] object-contain"
          />
          <div className="flex justify-between flex-1">
            <div>
              <h3 className="font-medium">{data.name}</h3>
              <p className="text-sm text-gray-500 mt-1">{data.description}</p>
            </div>
            {!!data.price && <p>{formatPrice({ price: data.price })}</p>}
          </div>
        </div>
      </div>
      {!!data.subPackages?.length && (
        <div
          className={cn(
            "my-3 max-h-0 overflow-hidden transition-all duration-300",
            expanded && "max-h-screen"
          )}
        >
          {data.subPackages.map((item, index) => (
            <div
              onClick={() => handleAccordionItemSelect(item)}
              className="p-4 border-b cursor-pointer border-gray-200 space-y-2"
              key={index}
            >
              <div className="flex justify-between">
                <div className="flex">
                  <h3
                    className={cn(
                      "font-medium bg-gray-100 transition-all flex gap-2 duration-300 hover:bg-gray-200 px-3 py-2 rounded-md",
                      subPackage == item.title &&
                        "border border-primary bg-primary/10 text-primary hover:bg-primary/30"
                    )}
                  >
                    {item.title}

                    <Popover>
                      <PopoverTrigger asChild>
                        <Info className="text-gray-500 text-sm" />
                      </PopoverTrigger>
                      <PopoverContent className="w-80 bg-white shadow p-4 text-gray-500 font-normal">
                        <p className="text-gray-800">{item.title}</p>
                        <p className="text-gray-600">{item.description}</p>
                        <div className="grid grid-cols-2 gap-2 pt-2">
                          {item.features.split(",").map((feature, index) => (
                            <p
                              key={index}
                              className="text-gray-500 flex gap-2 items-center"
                            >
                              <Check className="text-primary text-sm" />
                              {feature}
                            </p>
                          ))}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </h3>
                </div>
                {item.price && <p>{formatPrice({ price: item.price || 0 })}</p>}
              </div>
              <p className="text-gray-500">{item.description}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
