"use client";
import React, { useState, ReactNode } from "react";
import {
  Search,
  Glasses,
  Repeat,
  HelpCircle,
  Sun,
  User,
  ShoppingBag,
  Camera,
  Package,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Wrapper } from "@/components/common/wrapper";

// Types
type CategoryId =
  | "transitions"
  | "lens"
  | "reglaze"
  | "general"
  | "account"
  | "ordering"
  | "virtual"
  | "returns";

interface Category {
  id: CategoryId;
  label: string;
  icon: React.ReactNode;
}

interface Question {
  id: string;
  question: string;
  answer: ReactNode;
}

interface QuestionsMap {
  [key: string]: Question[];
}

const FAQs: React.FC = () => {
  const [activeCategory, setActiveCategory] =
    useState<CategoryId>("transitions");
  const [searchQuery, setSearchQuery] = useState<string>("");

  const categories: Category[] = [
    { id: "transitions", label: "Transitions Lenses", icon: <Sun size={20} /> },
    { id: "lens", label: "Lens Options", icon: <Glasses size={20} /> },
    { id: "reglaze", label: "Reglazing Service", icon: <Repeat size={20} /> },
    {
      id: "general",
      label: "General Questions",
      icon: <HelpCircle size={20} />,
    },
    { id: "account", label: "Account Help", icon: <User size={20} /> },
    {
      id: "ordering",
      label: "Ordering Process",
      icon: <ShoppingBag size={20} />,
    },
    { id: "virtual", label: "Virtual Try On", icon: <Camera size={20} /> },
    { id: "returns", label: "Returns & Delivery", icon: <Package size={20} /> },
  ];

  const allQuestions: QuestionsMap = {
    transitions: [
      {
        id: "transitions-1",
        question:
          "I am interested in Transitions® lenses, but I have been informed that they may not be effective while driving?",
        answer: (
          <p>
            In this case, we offer Transitions® XTRActive®, Transitions®
            XTRActive® Polarised™ and Transitions® Drivewear lenses, which,
            unlike the standard Transitions® Lenses, perform well when used
            behind a car windscreen. These lenses also darken slightly more and
            react more quickly than Transitions Signature 8.
          </p>
        ),
      },
      {
        id: "transitions-2",
        question:
          "What makes Transitions® Drivewear® lenses different from regular sunglasses?",
        answer: (
          <p>
            Regular sunglasses have a fixed tint that doesn't change, regardless
            of your surroundings. In contrast, photochromic sunglasses begin as
            clear lenses and darken in response to UV light, allowing them to
            adapt to various lighting conditions. While Transitions® lenses are
            a practical clear lens option for different environments, using
            prescription sunglasses is still advisable for extended time spent
            in the sun.
          </p>
        ),
      },
      {
        id: "transitions-3",
        question: "Are Transitions® lenses available for any frame?",
        answer: (
          <p>
            Yes, except for the 1.5 index lenses which is not suitable for
            rimless frames.
          </p>
        ),
      },
      {
        id: "transitions-4",
        question: "Is it possible to use Transitions® lenses with varifocals?",
        answer: (
          <p>
            Yes, Transitions® lenses work for different vision types like single
            vision, varifocals, and non-prescription lenses. However, they can't
            be used with bifocal lenses.
          </p>
        ),
      },
      {
        id: "transitions-5",
        question: "Is there UV protection in Transitions® lenses?",
        answer: (
          <p>
            Yes, every pair of Transitions® lenses includes a UV protection
            coating that effectively blocks 100% of UVA and UVB light.
          </p>
        ),
      },
      {
        id: "transitions-6",
        question:
          "Does Transitions® use a coating for its photochromic feature?",
        answer: (
          <p>
            The technology is included in the lenses, and their composition is
            designed to absorb more light when exposed to UV rays, resulting in
            a darker tint.
          </p>
        ),
      },
      {
        id: "transitions-7",
        question: "How do Transitions® lenses work?",
        answer: (
          <p>
            Transitions lenses are special glasses that automatically darken in
            sunlight and become clear indoors. They contain molecules that react
            to UV light, providing convenience by eliminating the need for
            separate sunglasses and protecting your eyes from harmful rays.
          </p>
        ),
      },
    ],
    lens: [
      {
        id: "lens-1",
        question: "Do you provide lenses for sunglasses?",
        answer: (
          <p>
            Yes, we do offer lens options for sunglasses. Our selections include
            solid tints, graduated tints, polarised tints, photochromic lenses,
            and mirror finish lenses. These options are available for both
            non-prescription and prescription lenses, including single vision,
            bifocals, and varifocals. If you require assistance in determining
            your specific needs, or if you would like to match lenses with
            existing frames, we are here to help.
          </p>
        ),
      },
      {
        id: "lens-2",
        question: "Do you offer non-prescription lenses?",
        answer: (
          <p>
            Yes, we offer a variety of non-prescription lenses. Our selection
            includes non-prescription sunglasses available in solid, graduated,
            or polarised tint options, all of which can have a mirror finish
            added. Many customers also seek plain clear lenses for fashion
            purposes. Furthermore, non-prescription blue light filter lenses,
            which help reduce fatigue and eye strain from prolonged screen use,
            have gained popularity in recent years.
          </p>
        ),
      },
      {
        id: "lens-3",
        question:
          "What is the difference between distance, intermediate, and near lenses?",
        answer: (
          <p>
            Distance lenses, or 'everyday lenses,' help those who have trouble
            seeing far away, like while driving or watching TV. Near vision
            lenses are for reading, aiding those who struggle with close-up
            tasks. Intermediate lenses cater to people who frequently look at
            computer screens or work at arm's length.
          </p>
        ),
      },
      // More lens questions...
    ],
    reglaze: [
      {
        id: "reglaze-1",
        question: "What is reglazing?",
        answer: (
          <p>
            This is a process in which we carefully take out the old lenses that
            are currently fitted in your frame and then replace them with fresh,
            new lenses that will enhance your vision and ensure a comfortable
            fit.
          </p>
        ),
      },
      // More reglaze questions...
    ],
    general: [
      {
        id: "general-1",
        question:
          "Are you a company that operates only online and is difficult to contact?",
        answer: (
          <p>
            We are UK-based industry specialists with over 50 years of
            experience. We're available for phone calls during working hours and
            respond to emails late into the night and on weekends. You can trust
            that professionals are here to assist you.
          </p>
        ),
      },
      // More general questions...
    ],
    account: [
      {
        id: "account-1",
        question: "Having trouble logging in?",
        answer: (
          <p>
            Try resetting your password first by visiting the forgotten password
            page or clicking "Forgotten your password?" on the login screen. If
            you still can't log in, please contact us.
          </p>
        ),
      },
    ],
    ordering: [
      {
        id: "ordering-1",
        question: "How do I know they will fit?",
        answer: (
          <p>
            We provide detailed frame measurements for comparison with your
            current glasses. Read more about choosing the right size. Our 100%
            satisfaction guarantee applies.
          </p>
        ),
      },
    ],
    virtual: [
      {
        id: "virtual-1",
        question: "Can I take a photo of myself in the virtual frames?",
        answer: (
          <p>
            Yes! Use the selfie function to save a photo and compare different
            glasses, which is helpful for making a decision.
          </p>
        ),
      },
      // More virtual questions...
    ],
    returns: [
      {
        id: "returns-1",
        question: "What is your returns policy?",
        answer: (
          <p>
            We offer a 30-day money-back guarantee. If you wish to return your
            glasses for any reason, please contact us via email or phone so we
            can facilitate the return process. Once we receive the glasses, we
            will reinstall your old lenses into the original frame (please
            ensure you include your old lenses with the return). Additionally,
            if a manufacturing defect occurs with your lenses after the 30-day
            period, we will be happy to replace them. For complete details,
            please refer to our terms and conditions.
          </p>
        ),
      },
      // More returns questions...
    ],
  };

  // Helper function to check if a string contains the search query
  const containsSearchQuery = (text: string): boolean => {
    return text.toLowerCase().includes(searchQuery.toLowerCase());
  };

  // Helper function to check if a ReactNode contains the search query
  const nodeContainsSearchQuery = (node: ReactNode): boolean => {
    if (typeof node === "string") {
      return containsSearchQuery(node);
    }

    if (React.isValidElement(node)) {
      const { children, ...props } = node.props as any;

      // Check if any prop is a string that contains the search query
      const propContainsQuery = Object.values(props).some(
        (prop) => typeof prop === "string" && containsSearchQuery(prop)
      );

      if (propContainsQuery) return true;

      // Recursively check children
      if (children) {
        if (Array.isArray(children)) {
          return children.some((child) => nodeContainsSearchQuery(child));
        }
        return nodeContainsSearchQuery(children);
      }
    }

    return false;
  };

  // Filter questions based on search query
  const filteredQuestions = searchQuery
    ? Object.values(allQuestions)
        .flat()
        .filter(
          (q) =>
            containsSearchQuery(q.question) || nodeContainsSearchQuery(q.answer)
        )
    : allQuestions[activeCategory] || [];

  return (
    <Wrapper vertical className=" bg-white">
      <div className="mb-8 text-center border-b pb-6">
        <h1 className="text-3xl font-bold mb-4">Frequently Asked Questions</h1>
        <p className="text-lg max-w-3xl mx-auto">
          Find answers to our most commonly asked questions. If you can't find
          what you're looking for, please don't hesitate to contact us.
        </p>
      </div>

      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search for questions..."
            className="w-full p-3 pl-10 border rounded-lg"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            aria-label="Search FAQs"
          />
          <Search className="absolute left-3 top-3.5 text-gray-400" size={18} />
        </div>
      </div>

      {!searchQuery ? (
        <div className="mb-6 overflow-x-auto">
          <div className="flex flex-nowrap space-x-2 pb-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`flex items-center whitespace-nowrap px-4 py-2 rounded-full ${
                  activeCategory === category.id
                    ? "bg-primary text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
                aria-pressed={activeCategory === category.id}
              >
                <span className="mr-2">{category.icon}</span>
                {category.label}
              </button>
            ))}
          </div>
        </div>
      ) : (
        <div className="mb-6">
          <p className="text-gray-700">
            {filteredQuestions.length}{" "}
            {filteredQuestions.length === 1 ? "result" : "results"} found for "
            {searchQuery}"
          </p>
        </div>
      )}

      {filteredQuestions.length === 0 ? (
        <div className="text-center py-12">
          <HelpCircle size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-xl font-semibold mb-2">No questions found</h3>
          <p className="text-gray-600 mb-6">
            We couldn't find any questions matching your search. Try different
            keywords or browse by category.
          </p>
          <button
            onClick={() => setSearchQuery("")}
            className="px-4 py-2 bg-primary/70 text-white rounded-lg hover:bg-primary/80"
          >
            Clear Search
          </button>
        </div>
      ) : (
        <Accordion type="single" collapsible className="w-full space-y-2">
          {filteredQuestions.map((item) => (
            <AccordionItem
              key={item.id}
              value={item.id}
              className="border rounded-lg overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-3 text-left font-semibold hover:bg-gray-50">
                {item.question}
              </AccordionTrigger>
              <AccordionContent className="px-4 py-3 bg-gray-50 border-t">
                {item.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}

      <div className="mt-10 pt-6 border-t">
        <div className="bg-primary/10 p-4 rounded-lg">
          <h3 className="font-semibold text-lg mb-2 flex items-center">
            <HelpCircle size={20} className="mr-2 text-primary" />
            Still have questions?
          </h3>
          <p className="mb-4">
            If you can't find the answer you're looking for, please contact our
            customer service team.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center justify-center px-4 py-2 bg-primary/80 text-white rounded-lg hover:bg-primary"
            >
              Email Us
            </a>
            <a
              href="tel:01157845219"
              className="flex items-center justify-center px-4 py-2 border border-primary text-primary rounded-lg hover:bg-primary/10"
            >
              Call 0115 784 5219
            </a>
          </div>
        </div>
      </div>
    </Wrapper>
  );
};

export default FAQs;
