"use client";

import React, { useRef, useEffect, useState } from "react";
import Webcam from "react-webcam";
import * as THREE from "three";
import * as tf from "@tensorflow/tfjs-core";
import "@tensorflow/tfjs-converter";
import "@tensorflow/tfjs-backend-webgl";
import {
  load,
  SupportedPackages,
  FaceLandmarksDetector,
} from "@tensorflow-models/face-landmarks-detection";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { useRouter } from "next/navigation";
import { api_interceptor } from "@/utils/api-interceptor";
import { PRODUCT_URL } from "@/constants/url";
import { getImageUrl } from "@/utils/image-url";

function isGLB(url: string): boolean {
  return (
    url.toLowerCase().endsWith(".glb") || url.toLowerCase().endsWith(".gltf")
  );
}

type Props = {
  slug: any;
};

export const VirtualTryOn: React.FC<Props> = ({ slug }) => {
  const webcamRef = useRef<Webcam>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [model, setModel] = useState<FaceLandmarksDetector | null>(null);
  const [glassesMesh, setGlassesMesh] = useState<THREE.Object3D | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [scene, setScene] = useState<THREE.Scene | null>(null);
  const [renderer, setRenderer] = useState<THREE.WebGLRenderer | null>(null);
  const [camera3D, setCamera3D] = useState<THREE.PerspectiveCamera | null>(
    null
  );
  const [alignmentMode, setAlignmentMode] = useState(true);
  const [faceCentered, setFaceCentered] = useState(false);
  const [showStartAnyway, setShowStartAnyway] = useState(false);
  const [frames, setFrames] = useState<string[]>([]);
  const [currentFrame, setCurrentFrame] = useState<string>("");
  const [stream, setStream] = useState<MediaStream | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const router = useRouter();

  useEffect(() => {
    const loadResources = async () => {
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 640, height: 480, facingMode: "user" },
        });
        setStream(mediaStream);

        if (webcamRef.current && webcamRef.current.video) {
          webcamRef.current.video.srcObject = mediaStream;
        }

        await tf.setBackend("webgl");
        await tf.ready();
        const loadedModel = await load(SupportedPackages.mediapipeFacemesh, {
          shouldLoadIrisModel: true,
          maxFaces: 1,
        });
        setModel(loadedModel);

        if (canvasRef.current) {
          const width = canvasRef.current.clientWidth;
          const height = canvasRef.current.clientHeight;
          const sceneNew = new THREE.Scene();
          const cameraNew = new THREE.PerspectiveCamera(
            75,
            width / height,
            0.1,
            1000
          );
          cameraNew.position.z = 5;
          const rendererNew = new THREE.WebGLRenderer({
            canvas: canvasRef.current,
            alpha: true,
            antialias: true, // Added for smoother rendering
          });
          rendererNew.setSize(width, height);
          rendererNew.setPixelRatio(window.devicePixelRatio); // Better quality

          // Use our own render loop instead of setAnimationLoop
          const animate = () => {
            rendererNew.render(sceneNew, cameraNew);
            animationFrameRef.current = requestAnimationFrame(animate);
          };
          animate();

          setScene(sceneNew);
          setCamera3D(cameraNew);
          setRenderer(rendererNew);
        }

        await fetchProduct(slug);
      } catch (error) {
        console.error("Initialization error:", error);
        setIsLoading(false);
      }
    };

    loadResources();

    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (renderer) {
        renderer.dispose();
      }
    };
  }, []);

  const fetchProduct = async (slug: string) => {
    try {
      const res = await api_interceptor.get(PRODUCT_URL + `/${slug}`);
      const images = [
        ...res.data.data.images,
        "/sunglass/1.glb",
        "/sunglass/2.glb",
        "/sunglass/3.glb",
      ];
      setFrames(images);
      setCurrentFrame(isGLB(images[0]) ? images[0] : getImageUrl(images[0]));
    } catch (error) {
      console.error("Product fetch error:", error);
    }
  };

  const loadGlasses = (uri: string) => {
    if (!scene) return;
    if (glassesMesh) {
      scene.remove(glassesMesh);
    }

    if (isGLB(uri)) {
      const loader = new GLTFLoader();
      loader.load(
        uri,
        (gltf) => {
          const model = gltf.scene;

          // Create a container to make positioning easier
          const container = new THREE.Group();
          container.add(model);

          // Adjust the model position within the container
          model.scale.set(0.5, 0.5, 0.5);
          model.position.set(0, 0, 0);

          // Add to scene
          scene.add(container);
          setGlassesMesh(container);
          setIsLoading(false);
        },
        (xhr) => {
          console.log((xhr.loaded / xhr.total) * 100 + "% loaded");
        },
        (error) => {
          console.error("Error loading GLB model:", error);
          setIsLoading(false);
        }
      );
    } else {
      const textureLoader = new THREE.TextureLoader();
      textureLoader.load(
        uri,
        (texture) => {
          texture.colorSpace = THREE.SRGBColorSpace;

          // Create a container for the 2D glasses
          const container = new THREE.Group();

          const geometry = new THREE.PlaneGeometry(1, 0.5);
          const material = new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
          });
          const glasses = new THREE.Mesh(geometry, material);

          container.add(glasses);
          scene.add(container);
          setGlassesMesh(container);
          setIsLoading(false);
        },
        undefined,
        (error) => {
          console.error("Error loading texture:", error);
          setIsLoading(false);
        }
      );
    }
  };

  useEffect(() => {
    if (currentFrame && scene) {
      loadGlasses(currentFrame);
    }
  }, [currentFrame, scene]);

  useEffect(() => {
    const detectAndPositionGlasses = async () => {
      if (
        !webcamRef.current?.video ||
        !model ||
        !glassesMesh ||
        webcamRef.current.video.readyState !== 4
      ) {
        return; // Exit if video, model, or glasses are not ready
      }

      try {
        const faceEstimates: any[] = await model.estimateFaces({
          input: webcamRef.current.video,
          flipHorizontal: false, // Set to false as webcam is already mirrored
        });

        if (faceEstimates.length === 0) {
          return; // Exit if no faces are detected
        }

        setIsLoading(false); // Face detected, so loading is complete

        const keypoints = faceEstimates[0].scaledMesh as number[][];

        // Get more stable keypoints for position calculations
        const leftEye = keypoints[468]; // Left iris center
        const rightEye = keypoints[473]; // Right iris center
        const nosePoint = keypoints[4]; // Nose tip
        const foreheadPoint = keypoints[10]; // Forehead point
        const leftTemple = keypoints[162]; // Left temple
        const rightTemple = keypoints[389]; // Right temple

        const videoWidth = webcamRef.current.video.videoWidth;
        const videoHeight = webcamRef.current.video.videoHeight;

        // Calculate the midpoint between the eyes
        const eyeMidpointX = (leftEye[0] + rightEye[0]) / 2;
        const eyeMidpointY = (leftEye[1] + rightEye[1]) / 2;

        // Normalize the midpoint coordinates
        const normalizedMidpointX = eyeMidpointX / videoWidth;
        const normalizedMidpointY = eyeMidpointY / videoHeight;

        const distanceFromCenter = Math.sqrt(
          Math.pow(normalizedMidpointX - 0.5, 2) +
            Math.pow(normalizedMidpointY - 0.5, 2)
        );

        // Adjust the threshold as needed
        if (distanceFromCenter < 0.15) {
          if (!faceCentered) {
            setFaceCentered(true);
            setTimeout(() => setAlignmentMode(false), 500);
          }
        } else {
          setFaceCentered(false);
        }

        if (alignmentMode) return;

        // Calculate the distance between the eyes for scaling
        const eyeDistance = Math.sqrt(
          Math.pow(rightEye[0] - leftEye[0], 2) +
            Math.pow(rightEye[1] - leftEye[1], 2)
        );

        // Calculate the face width for better scaling
        const faceWidth = Math.sqrt(
          Math.pow(rightTemple[0] - leftTemple[0], 2) +
            Math.pow(rightTemple[1] - leftTemple[1], 2)
        );

        const is3DModel =
          glassesMesh.children.length > 0 &&
          !(glassesMesh.children[0] as any).isMesh;

        // Better scaling factors based on face proportions
        const scaleFactor3D = eyeDistance / 160;
        const scaleFactor2D = faceWidth / 240;
        const scale = is3DModel ? scaleFactor3D : scaleFactor2D;

        // Calculate face tilt and yaw for better 3D positioning
        const faceForward = new THREE.Vector3(
          nosePoint[0] - foreheadPoint[0],
          nosePoint[1] - foreheadPoint[1],
          nosePoint[2] - foreheadPoint[2]
        ).normalize();

        // Calculate face horizontal angle (yaw)
        const leftToRight = new THREE.Vector3(
          rightEye[0] - leftEye[0],
          rightEye[1] - leftEye[1],
          rightEye[2] - leftEye[2]
        ).normalize();

        // Position adjustments
        glassesMesh.position.x =
          (eyeMidpointX - videoWidth / 2) * (is3DModel ? -0.002 : -0.0018);
        glassesMesh.position.y =
          (eyeMidpointY - videoHeight / 2) * (is3DModel ? -0.002 : -0.0018) -
          (is3DModel ? 0.05 : 0.1);
        glassesMesh.position.z = is3DModel ? 0.5 : 1;

        // Apply scaling
        glassesMesh.scale.set(scale, scale, scale);

        // Calculate and apply face rotation
        // Roll (around z-axis)
        const eyeVector = new THREE.Vector2(
          rightEye[0] - leftEye[0],
          rightEye[1] - leftEye[1]
        );
        const rollAngle = Math.atan2(eyeVector.y, eyeVector.x);

        // Pitch (around x-axis - looking up/down)
        const pitchAngle = Math.atan2(faceForward.y, faceForward.z) * 0.5;

        // Yaw (around y-axis - looking left/right)
        const yawFactor = (normalizedMidpointX - 0.5) * 1.5;

        // Apply rotations in correct order
        glassesMesh.rotation.set(pitchAngle, yawFactor, rollAngle);

        // Dynamically resize 2D frame geometry if needed
        if (!is3DModel && glassesMesh.children.length > 0) {
          const glassesPlane = glassesMesh.children[0] as THREE.Mesh;
          if (glassesPlane.geometry instanceof THREE.PlaneGeometry) {
            const aspectRatio = 2.2; // Adjust based on your image
            const width = faceWidth / 130; // Better scaling
            const height = width / aspectRatio;
            glassesPlane.geometry.dispose();
            glassesPlane.geometry = new THREE.PlaneGeometry(width, height);
          }
        }
      } catch (error) {
        console.error("Error during face detection or positioning:", error);
      }
    };

    // Use requestAnimationFrame for smoother tracking instead of setInterval
    let animationId: number;
    const animate = () => {
      detectAndPositionGlasses();
      animationId = requestAnimationFrame(animate);
    };
    animate();

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, [model, glassesMesh, alignmentMode]);

  useEffect(() => {
    if (alignmentMode) {
      const timeout = setTimeout(() => {
        setShowStartAnyway(true);
      }, 10000);
      return () => clearTimeout(timeout);
    }
  }, [alignmentMode]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current && renderer && camera3D) {
        const width = canvasRef.current.clientWidth;
        const height = canvasRef.current.clientHeight;
        camera3D.aspect = width / height;
        camera3D.updateProjectionMatrix();
        renderer.setSize(width, height);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [renderer, camera3D]);

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="border-b border-black/20 text-center mb-4">
        <h1 className="text-lg font-semibold py-4">Virtual Try-On</h1>
      </div>

      <div className="w-full flex justify-between items-center px-4 py-2">
        <button
          className="px-4 py-2 bg-black text-white rounded-md"
          onClick={() => {
            if (stream) {
              stream.getTracks().forEach((track) => track.stop());
            }
            if (animationFrameRef.current) {
              cancelAnimationFrame(animationFrameRef.current);
            }
            if (renderer) {
              renderer.dispose();
            }
            router.push("/product");
          }}
        >
          ← Back to Products
        </button>
      </div>

      <div className="relative mx-auto w-[90vw] max-w-[400px] aspect-[3/4] rounded-md overflow-hidden shadow-lg bg-black/5">
        {isLoading && (
          <div className="absolute top-0 left-0 w-full h-full bg-white/60 flex flex-col justify-center items-center z-20">
            <div className="w-12 h-12 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin"></div>
            <h3 className="mt-4">Loading resources...</h3>
          </div>
        )}

        {!isLoading && alignmentMode && (
          <div className="absolute inset-0 flex flex-col items-center justify-center z-30 bg-black/50">
            <div className="relative">
              <div
                className="w-60 h-60 rounded-full border-4 border-white animate-pulse"
                style={{
                  background:
                    "radial-gradient(circle at center, transparent 40%, rgba(0,0,0,0.6) 42%)",
                }}
              ></div>
              <p className="text-white mt-6 text-center text-sm">
                Align your face inside the circle
              </p>
              {showStartAnyway && (
                <div className="flex justify-center">
                  <button
                    onClick={() => setAlignmentMode(false)}
                    className="mt-6 px-4 py-2 bg-white text-black rounded-md text-sm"
                  >
                    Start Anyway
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        <Webcam
          ref={webcamRef}
          mirrored
          autoPlay
          playsInline
          videoConstraints={{
            width: 640,
            height: 480,
            facingMode: "user",
          }}
          className="absolute inset-0 object-cover w-full h-full"
        />

        <canvas
          ref={canvasRef}
          width={640}
          height={480}
          className="absolute inset-0 w-full h-full pointer-events-none"
        />
      </div>

      {frames.length > 0 && (
        <div className="flex flex-wrap gap-4 overflow-x-auto mt-6 mb-6 px-2 justify-center">
          {frames.map((frame, idx) => (
            <button
              key={idx}
              className={`border-2 rounded-md overflow-hidden w-24 h-24 flex-shrink-0 ${
                currentFrame === (isGLB(frame) ? frame : getImageUrl(frame))
                  ? "border-blue-500"
                  : "border-gray-300"
              }`}
              onClick={() =>
                setCurrentFrame(isGLB(frame) ? frame : getImageUrl(frame))
              }
            >
              <img
                src={getImageUrl(frame)}
                alt={`Frame ${idx + 1}`}
                className="w-full h-full object-contain"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
