import { ADDRESS, EMAIL, PHONE } from "@/config/app-config";
import { Copyright } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ChatBubble } from "./chat-bubble";

export const Footer = () => {
  const sections = [
    {
      header: "Help",
      items: [
        { label: "Privacy Policy", link: "/privacy-policy" },
        { label: "Returns + Exchanges", link: "/returns-exchanges" },
        { label: "Shipping", link: "/shipping" },
        { label: "Terms & Conditions", link: "/terms-conditions" },
        { label: "FAQ", link: "/faq" },
      ],
    },
    {
      header: "About us",
      items: [
        { label: "Our Story", link: "/our-story" },
        { label: "Visit our store", link: "/store" },
        { label: "Contact us", link: "/help" },
        { label: "About us", link: "/about-us" },
      ],
    },
    {
      header: "Shop All",
      items: [
        { label: "All Glasses", link: "/glasses" },
        { label: "All Contacts", link: "/contacts" },
        { label: "All Women's Glasses", link: "/glasses/women" },
        { label: "All Men's Glasses", link: "/glasses/men" },
        { label: "All Kid's Glasses", link: "/glasses/kids" },
        { label: "All Sunglasses", link: "/glasses/sunglasses" },
        {
          label: "All Prescription Sunglasses",
          link: "/glasses/prescription-sunglasses",
        },
      ],
    },
  ];

  return (
    <footer className="bg-white px-10 pt-24 border-t border-t-gray-200">
      <ChatBubble />
      <div className="grid grid-cols-1 md:grid-cols-4 gap-10 pb-10 text-sm">
        {/* Logo & Contact Info */}
        <div className="space-y-5">
          <Image
            src="/logo.svg"
            alt="logo"
            width={80}
            height={80}
            className="h-auto w-28"
          />
          <p>Address: {ADDRESS}</p>
          <p>
            Email: <Link href={`mailto:${EMAIL}`}>{EMAIL}</Link>
          </p>
          <p>
            Phone: <Link href={`tel:${PHONE}`}>{PHONE}</Link>
          </p>
        </div>

        {/* Footer Sections */}
        {sections.map((section, index) => (
          <div key={index}>
            <h3 className="text-xl font-semibold mb-5">{section.header}</h3>

            <ul className="space-y-3">
              {section.items.map((item, i) => (
                <li key={i}>
                  <Link
                    href={item.link}
                    className="text-gray-600 hover:text-black hover:font-medium"
                  >
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      <div className="py-4 border-t border-gray-200 w-100">
        <div className="grid grid-cols-2 gap-5">
          <p className="flex text-xs items-center">
            <Copyright size={12} className="mr-1" /> 2025 Quality Fast Specs.
            All Rights Reserved
          </p>

          <p className="flex justify-end">
            <img src="/payment-methods.svg" alt="" />
          </p>
        </div>
      </div>
    </footer>
  );
};
