"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface Step {
  label: string;
  icon?: React.ReactNode;
}

interface ProgressBarProps extends React.HTMLAttributes<HTMLDivElement> {
  steps: Step[];
  currentStep: number; // 0-based index
  barColor?: string;
  completedColor?: string;
  labelClassName?: string;
  iconClassName?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  steps,
  currentStep,
  barColor = "bg-primary/20",
  completedColor = "bg-primary",
  labelClassName,
  iconClassName,
  className,
}) => {
  return (
    <div
      className={cn(
        "flex justify-between items-start w-full relative pt-8",
        className
      )}
    >
      {steps.map((step, index) => {
        const isCompleted = index < currentStep;
        const isCurrent = index === currentStep;

        return (
          <div
            key={step.label}
            className="relative flex flex-col items-center text-center flex-1"
          >
            {/* Dot */}
            <div
              className={cn(
                "z-10 w-4 h-4 rounded-full border-2",
                isCompleted || isCurrent
                  ? `${completedColor} border-transparent`
                  : "border-primary/20 bg-white"
              )}
              style={
                isCompleted || isCurrent
                  ? { backgroundColor: completedColor }
                  : {}
              }
            />

            {/* Connecting Line */}
            {index !== steps.length - 1 && (
              <div
                className={`absolute top-2 left-1/2 w-full h-0.5 ${barColor} z-0`}
              >
                <div
                  className={cn(
                    `absolute top-0 left-0 h-full transition-all duration-300 ${completedColor}`
                  )}
                  style={{
                    width: isCompleted ? "100%" : isCurrent ? "50%" : "0%",
                    backgroundColor: completedColor,
                  }}
                />
              </div>
            )}

            {/* Label + Icon */}
            <div className="pt-6 space-y-1">
              {step.icon && (
                <div
                  className={cn(
                    "w-4 h-4 mx-auto mb-3",
                    isCompleted
                      ? "text-green-600"
                      : isCurrent
                      ? "text-primary"
                      : "text-primary/20",
                    iconClassName
                  )}
                >
                  {step.icon}
                </div>
              )}

              <div
                className={cn(
                  "text-sm font-medium",
                  isCompleted || isCurrent ? "text-black" : "text-gray-400",
                  labelClassName
                )}
              >
                {step.label}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

ProgressBar.displayName = "ProgressBar";
