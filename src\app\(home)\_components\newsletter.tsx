"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { api_interceptor } from "@/utils/api-interceptor";
import { toast } from "sonner";
import { getErrorMessage } from "@/utils/error-message";

export const NewsLetter = () => {
  const [email, setEmail] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  const onSubmit = (e: any) => {
    e.preventDefault();
    setLoading(true);
    api_interceptor
      .post("/newsletter", { email })
      .then(() => {
        setEmail("");
        toast.success("Email submitted successfully");
      })
      .catch((e) => toast.error(getErrorMessage(e, "Something went wrong")))
      .finally(() => setLoading(false));
  };

  return (
    <div className="bg-[#F4F4F4] flex flex-col items-center p-12 gap-7">
      <p className="text-center font-semibold">
        Sign Up to Our Newsletter to Receive Exclusive Offers and Savings
      </p>

      <form
        onSubmit={onSubmit}
        className="flex flex-col sm:flex-row gap-6 sm:gap-2 w-full justify-center"
      >
        <Input
          placeholder="Enter your email address"
          className="sm:w-80 sm:max-w-[300px]"
          type="email"
          required
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />

        <Button className="bg-black" disabled={loading}>
          {loading ? "Subscribing" : "Subscribe"}
        </Button>
      </form>
    </div>
  );
};
