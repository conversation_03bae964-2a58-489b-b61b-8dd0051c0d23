"use client";
import React from "react";

// NEXT AUTH
import { SessionProvider } from "next-auth/react";

// REDUX
import store from "./store";
import { Provider } from "react-redux";

// PROVIDERS
import { ProviderChildren } from "./provider-children";

export function StoreProvider({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <Provider store={store}>
        <ProviderChildren>{children}</ProviderChildren>
      </Provider>
    </SessionProvider>
  );
}
