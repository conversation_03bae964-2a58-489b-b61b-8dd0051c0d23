"use client";
import React, { useEffect, useState } from "react";
import { Wrapper } from "@/components/common/wrapper";
import Image from "next/image";
import Link from "next/link";
import { api_interceptor } from "@/utils/api-interceptor";
import { DEAL_URL } from "@/constants/url";
import { IDeal } from "@/interfaces";
import { toast } from "sonner";
import { getErrorMessage } from "@/utils/error-message";
import { getImageUrl } from "@/utils/image-url";

const page = () => {
  const [deals, setDeals] = useState<IDeal[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    setLoading(true);
    api_interceptor
      .get(DEAL_URL)
      .then((res) => setDeals(res.data.data))
      .catch((err) => toast(getErrorMessage(err, "Something went wrong")))
      .finally(() => setLoading(false));
    return () => {};
  }, []);

  return (
    <Wrapper vertical>
      <div className="grid md:grid-cols-2 gap-7">
        {deals.map((deal, index) => (
          <div key={index} className="border border-gray-200 p-4 rounded-md">
            <div className="aspect-video">
              <Image
                src={getImageUrl(deal.image)}
                width={500}
                height={500}
                alt="Deal image"
                className="w-full h-full object-cover rounded-md"
              />
            </div>
            <div className="my-3">
              <p className="text-xl font-medium">{deal.title}</p>
              <p>{deal.shortDescription}</p>
              <p dangerouslySetInnerHTML={{ __html: deal.longDescription }} />
              <p className="font-semibold">
                Use Code:{" "}
                <span className="text-primary">{deal.coupon.code}</span>
              </p>
            </div>
            <Link
              href={"/product"}
              className="bg-primary text-white py-2 px-3 rounded-md transition-all duration-300 hover:bg-primary/70"
            >
              Grab Deal
            </Link>
          </div>
        ))}
      </div>
    </Wrapper>
  );
};

export default page;
