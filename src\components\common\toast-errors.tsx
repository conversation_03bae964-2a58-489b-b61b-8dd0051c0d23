"use client";
import React, { useEffect } from "react";
import { toast } from "sonner";

interface IToastErrors {
  error?: string | string[];
  success?: string;
}

export const ToastErrors: React.FC<IToastErrors> = ({ error, success }) => {
  useEffect(() => {
    if (error?.length) {
      if (typeof error === "string") {
        toast("Error", {
          description: error,
          descriptionClassName: "description-text",
        });
      } else {
        error.forEach((err) =>
          toast("Error", {
            description: err,
            descriptionClassName: "description-text",
          })
        );
      }
    }
    return () => {};
  }, [error]);

  useEffect(() => {
    if (success) {
      toast("Success", {
        description: success,
        descriptionClassName: "description-text",
      });
    }
    return () => {};
  }, [success]);

  return <div></div>;
};
