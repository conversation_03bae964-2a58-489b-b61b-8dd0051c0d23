"use client";
import React, { useState, useEffect } from "react";
import { formatPrice } from "@/utils/format-price";
import { <PERSON>, Heart } from "lucide-react";
// import { Button } from "../ui/button";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";
import Link from "next/link";
import { IProduct } from "@/interfaces";
import { getImageUrl } from "@/utils/image-url";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { toast } from "sonner";
import {
  addToWishlist,
  fetchWishlist,
  removeFromWishlist,
} from "@/store/slices/wishlist-slice";

export const Product = ({ product }: { product?: IProduct }) => {
  const colorVariation = product?.attributes?.COLOR || [];
  const { user } = useSelector((state: RootState) => state.user);
  const { wishlist } = useSelector((state: RootState) => state.wishlist);
  const [thumbnail, setThumbnail] = useState(product?.thumbnail ?? "");

  const [favorite, setFavorite] = useState<boolean>(false);
  const [selectedColor, setSelectedColor] = useState("");

  useEffect(() => {
    setFavorite(!!wishlist.find((item) => item.product._id === product?._id));
    return () => {};
  }, [wishlist.length]);

  useEffect(() => {
    if (colorVariation.length) {
      setSelectedColor(colorVariation[0]);
    } else {
      setSelectedColor("");
    }
    return () => {};
  }, [colorVariation.length]);

  useEffect(() => {
    const currentVariation = product?.variations?.find(
      (item) => item.attributes[`color-${selectedColor?.toLowerCase()}`]
    );
    setThumbnail(currentVariation?.thumbnail ?? product?.thumbnail ?? "");
    return () => {};
  }, [[product, selectedColor]]);

  const toggleFavorite = () => {
    if (!user) {
      toast.error("Error", {
        description: "Please login to add product to wishlist",
        descriptionClassName: "description-text",
      });
      return;
    }
    if (!favorite) {
      store
        .dispatch(addToWishlist({ productId: product?._id ?? "" }))
        .then((res: any) => {
          if (res.meta.requestStatus === "rejected") {
            toast.error("Error", {
              description: res.payload.message,
              descriptionClassName: "description-text",
            });
          } else {
            toast.success("Success", {
              description: "Product added to wishlist",
              descriptionClassName: "description-text",
            });
            store.dispatch(fetchWishlist({}));
          }
        });
    } else {
      store
        .dispatch(removeFromWishlist({ productId: product?._id ?? "" }))
        .then((res: any) => {
          if (res.meta.requestStatus === "rejected") {
            toast.error("Error", {
              description: res.payload.message,
              descriptionClassName: "description-text",
            });
          } else {
            toast.success("Success", {
              description: "Product removed from wishlist",
              descriptionClassName: "description-text",
            });
          }
        });
    }

    setFavorite(!favorite);
  };

  return (
    <div className="space-y-2">
      <div className="bg-gray-100 p-5 rounded-lg space-y-6 md:space-y-8 lg:space-y-10 flex flex-col justify-center items-center">
        <div
          className="ml-auto cursor-pointer bg-primary/20 rounded-full p-2"
          onClick={toggleFavorite}
        >
          <Heart
            className={cn(
              "text-primary size-5",
              favorite ? "fill-primary" : "fill-transparent"
            )}
          />
        </div>
        <Link href={`/product/${product?.slug}`} className="">
          <Image
            src={getImageUrl(thumbnail)}
            height={400}
            width={400}
            className="mx-auto h-28 w-full object-contain"
            alt="Product image"
          />
        </Link>

        <div className="flex justify-center">
          <Button
            size={"sm"}
            className="p-0 transition-all duration-300 bg-primary/25 font-semibold text-primary hover:text-white text-sm rounded-full"
          >
            <Link
              className="flex items-center gap-2 cursor-pointer px-2 py-1 "
              href={`/product/${product?.slug}/tryon`}
            >
              <Camera />
              Try On
            </Link>
          </Button>
        </div>
      </div>
      <div className="h-2" />
      <Link href={`/product/${product?.slug}`}>
        <h1 className="font-medium font-sans text-gray-700 line-clamp-2">
          {product?.name}
        </h1>
      </Link>
      <div className="flex items-center space-x-2">
        <p className="font-medium line-through text-gray-400">
          {formatPrice({ price: product?.mrp ?? 0 })}
        </p>
        <p className="font-semibold text-primary text-xl">
          {formatPrice({ price: product?.basePrice ?? 0 })}
        </p>
      </div>
      <p className="first-letter:uppercase">{selectedColor?.toLowerCase()}</p>
      <div className="flex space-x-2">
        {colorVariation.map((variation, index) => (
          <div
            key={index}
            onClick={() => setSelectedColor(variation)}
            className={cn(
              "rounded-full p-[2px] cursor-pointer border border-white",
              selectedColor === variation && "border-gray-400"
            )}
          >
            <div
              style={{ backgroundColor: variation }}
              className="h-4 w-4 rounded-full border"
            />
          </div>
        ))}
      </div>

      {/* <div className="flex flex-col sm:flex-row justify-between gap-3">
        <div className="flex items-center space-x-1">
          <Star color="orange" fill="orange" size={20} />
          <p className="font-medium">4.5</p>
          <p className="text-gray-600 text-sm">(1400)</p>
        </div>
        <Button className="px-3">Add to cart</Button>
      </div> */}
    </div>
  );
};
