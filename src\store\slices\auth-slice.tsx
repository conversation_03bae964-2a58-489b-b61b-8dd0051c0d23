// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACES
import {
  IAuthSlice,
  IForgotPassword,
  IResetPassword,
  IRegister,
  IVerifyEmail,
} from "@/interfaces";
import { FetchDataRejected } from "@/interfaces/reject-type";

// CONSTANTS
import {
  REGISTER_URL,
  FORGOT_PASSWORD_URL,
  RESET_PASSWORD_URL,
  VERIFY_EMAIL_URL,
} from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { getErrorMessage } from "@/utils/error-message";

// Initial State
const initialState: IAuthSlice = {
  loading: false,
  error: [],
  success: "",
};

// REGISTER USER
export const registerUser = createAsyncThunk<
  string, // Return type (success message)
  IRegister, // Payload type (registration data)
  FetchDataRejected // Error type
>("auth/register", async (userData, { rejectWithValue }) => {
  return api_interceptor
    .post(REGISTER_URL, userData)
    .then((response) => {
      return response.data.message;
    })
    .catch((err) =>
      rejectWithValue({
        message: getErrorMessage(err, "Registration failed"),
      })
    );
});

// FORGOT PASSWORD
export const forgotPassword = createAsyncThunk<
  string,
  IForgotPassword,
  FetchDataRejected
>("auth/forgotPassword", async ({ email }, { rejectWithValue }) => {
  try {
    const response = await api_interceptor.post(FORGOT_PASSWORD_URL, { email });
    return response.data.message;
  } catch (err: any) {
    return rejectWithValue({
      message: getErrorMessage(err, "Password reset request failed"),
    });
  }
});

// RESET PASSWORD
export const resetPassword = createAsyncThunk<
  string,
  IResetPassword,
  FetchDataRejected
>("auth/resetPassword", async (resetData, { rejectWithValue }) => {
  try {
    const response = await api_interceptor.post(RESET_PASSWORD_URL, resetData);
    return response.data.message;
  } catch (err: any) {
    return rejectWithValue({
      message: getErrorMessage(err, "Reset password failed"),
    });
  }
});

// VERIFY EMAIL
export const verifyEmail = createAsyncThunk<
  string,
  IVerifyEmail,
  FetchDataRejected
>("auth/verifyEmail", async (payload, { rejectWithValue }) => {
  try {
    const response = await api_interceptor.post(VERIFY_EMAIL_URL, payload);
    return response.data.message;
  } catch (err: any) {
    return rejectWithValue({
      message: getErrorMessage(err, "Verify email failed"),
    });
  }
});

// AUTH SLICE
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.loading = false;
      state.success = "";
      state.error = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // REGISTER USER
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.error = [];
        state.success = "";
      })
      .addCase(registerUser.fulfilled, (state, { payload }) => {
        state.loading = false;
        state.success = payload;
      })
      .addCase(registerUser.rejected, (state, { payload }) => {
        state.loading = false;
        state.error = payload?.message!;
      })

      // FORGOT PASSWORD
      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
        state.error = [];
        state.success = "";
      })
      .addCase(forgotPassword.fulfilled, (state, { payload }) => {
        state.loading = false;
        state.success = payload;
      })
      .addCase(forgotPassword.rejected, (state, { payload }) => {
        state.loading = false;
        state.error = payload?.message!;
      })

      // RESET PASSWORD
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
        state.error = [];
        state.success = "";
      })
      .addCase(resetPassword.fulfilled, (state, { payload }) => {
        state.loading = false;
        state.success = payload;
      })
      .addCase(resetPassword.rejected, (state, { payload }) => {
        state.loading = false;
        state.error = payload?.message!;
      })

      // VERIFY EMAIL
      .addCase(verifyEmail.pending, (state) => {
        state.loading = true;
        state.error = [];
        state.success = "";
      })
      .addCase(verifyEmail.fulfilled, (state, { payload }) => {
        state.loading = false;
        state.success = payload;
      })
      .addCase(verifyEmail.rejected, (state, { payload }) => {
        state.loading = false;
      });
  },
});

// EXPORT ACTIONS & REDUCER
export const { resetAuthState } = authSlice.actions;
export default authSlice.reducer;
