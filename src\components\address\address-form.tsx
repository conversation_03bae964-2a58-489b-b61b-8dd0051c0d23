"use client";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { IAddress } from "@/interfaces/address";
import {
  createAddress,
  fetchAddresses,
  updateAddress,
} from "@/store/slices/address-slice";
import store, { RootState } from "@/store/store";
import Link from "next/link";
import { useForm, Controller } from "react-hook-form";
import { toast } from "sonner";
import { useSelector } from "react-redux";

type AddressFormProps = {
  defaultValues?: IAddress;
  id?: string;
  onCancel?: VoidFunction;
};

const countryOptions = [
  { label: "India", value: "India" },
  { label: "USA", value: "USA" },
  { label: "Canada", value: "Canada" },
];

const stateOptions = [
  { label: "Punjab", value: "Punjab" },
  { label: "Delhi", value: "Delhi" },
  { label: "California", value: "California" },
];

export const AddressForm: React.FC<AddressFormProps> = ({
  defaultValues,
  id,
  onCancel,
}) => {
  const {
    address: { address },
    user: { user },
  } = useSelector((state: RootState) => state);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    control,
  } = useForm<IAddress>({
    defaultValues,
  });

  const onCancelHandler = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const onSubmit = (data: IAddress) => {
    if (id) {
      data._id = id;
      store.dispatch(updateAddress(data)).then((res: any) => {
        if (res.meta.requestStatus === "fulfilled") {
          toast("Success", {
            description: res.payload?.toString() || "Address updated",
            descriptionClassName: "description-text",
          });
        } else {
          toast("Error", {
            description: res.payload?.message || "Error updating address",
            descriptionClassName: "description-text",
          });
        }
      });
      return;
    }

    store.dispatch(createAddress(data)).then((res: any) => {
      if (res.meta.requestStatus === "fulfilled") {
        reset();
        toast("Success", {
          description: res.payload?.toString() || "Address added successfully",
          descriptionClassName: "description-text",
        });
      } else {
        toast("Error", {
          description: res.payload?.message || "Error adding address",
          descriptionClassName: "description-text",
        });
      }
    });
  };

  useEffect(() => {
    if (!user) return;
    if (address.length && id) {
      const currentAddress = address.find((address) => address._id === id);
      if (currentAddress) {
        reset(currentAddress);
      } else {
        toast("Error", {
          description: "Address not found",
          descriptionClassName: "description-text",
        });
      }
    } else {
      store.dispatch(fetchAddresses());
    }
    return () => {};
  }, [address.length, user]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <h2 className="text-xl font-semibold mb-2">
        {id ? "Edit Address" : "Add New Address"}
      </h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-1">
          <Input
            label="Full Name"
            placeholder="John Doe"
            className="border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            {...register("fullName", { required: "Name is required" })}
          />

          {errors.fullName && (
            <span className="text-red-500 text-sm">
              {errors.fullName.message}
            </span>
          )}
        </div>

        <div className="space-y-1">
          <Input
            label="Phone Number"
            placeholder="9876543210"
            className="border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            {...register("phone", {
              required: "Phone is required",
              pattern: {
                value: /^[0-9]{10}$/,
                message: "Enter a valid 10-digit number",
              },
            })}
          />

          {errors.phone && (
            <span className="text-red-500 text-sm">{errors.phone.message}</span>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-1">
          <Input
            label="Address"
            placeholder="123 Main St"
            className="border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            {...register("address", { required: "Address is required" })}
          />

          {errors.address && (
            <span className="text-red-500 text-sm">
              {errors.address.message}
            </span>
          )}
        </div>

        <div className="space-y-1">
          <Input
            label="City"
            placeholder="City name"
            className="border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            {...register("city", { required: "City is required" })}
          />

          {errors.city && (
            <span className="text-red-500 text-sm">{errors.city?.message}</span>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-1">
          <Select
            label="Country"
            options={countryOptions}
            className="border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            {...register("country", { required: "Country is required" })}
          />

          {errors.country && (
            <span className="text-red-500 text-sm">
              {errors.country?.message}
            </span>
          )}
        </div>

        <div className="space-y-1">
          <Select
            label="State"
            options={stateOptions}
            className="border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            {...register("state", { required: "State is required" })}
          />

          {errors.state && (
            <span className="text-red-500 text-sm">
              {errors.state?.message}
            </span>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-1">
          <Input
            label="Postal Code"
            placeholder="123456"
            className="border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            {...register("postalCode", {
              required: "Postal code is required",
              pattern: {
                value: /^[0-9]{5,6}$/,
                message: "Enter a valid 5 or 6 digit postal code",
              },
            })}
          />

          {errors.postalCode && (
            <span className="text-red-500 text-sm">
              {errors.postalCode?.message}
            </span>
          )}
        </div>
      </div>
      <Controller
        control={control}
        name="isDefault"
        defaultValue={false}
        render={({ field }) => (
          <div className="flex items-center space-x-2">
            <Checkbox
              id="default-address"
              checked={field.value}
              onCheckedChange={field.onChange}
            />
            <label
              htmlFor="default-address"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Set as default address
            </label>
          </div>
        )}
      />

      <div className="">
        <Button
          onClick={onCancelHandler}
          type="button"
          className="outline bg-white text-primary hover:text-white w-32 mr-2 p-0"
        >
          Cancel
        </Button>

        <Button type="submit" className="w-32">
          {id ? "Update Address" : "Add Address"}
        </Button>
      </div>
    </form>
  );
};
