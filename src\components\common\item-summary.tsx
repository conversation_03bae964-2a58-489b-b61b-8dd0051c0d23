import Link from "next/link";
import { X } from "lucide-react";
import { formatPrice } from "@/utils/format-price";
import { IWishlist } from "@/interfaces";
import { getImageUrl } from "@/utils/image-url";

type CartItemProps = {
  item: IWishlist;
  index: number;
  length: number;
  onRemove: (item: any, index: number) => void;
};

const ItemSummary = ({ item, index, length, onRemove }: CartItemProps) => {
  return (
    <div
      className={`${index !== length - 1 ? "border-b border-gray-200" : ""}`}
    >
      <div className="flex items-start p-3">
        <img
          className="w-20 mr-5"
          src={getImageUrl(item.product.thumbnail || "")}
          alt={item.product.name}
        />
        <div className="mr-5">
          <p className="mb-2">
            <Link
              href={`/product/${item.product.slug}`}
              className="font-semibold text-text-accent hover:underline hover:text-text-accent/50"
            >
              {item.product.name}
            </Link>
          </p>
          {/* {item.variation && (
            <p className="text-sm mb-2">{item.variation.sku}</p>
            )} */}
          {/* <p className="text-sm mb-2">XS (110-118mm)</p> */}

          <div className="mb-7">
            {Object.keys(item.variation?.attributes || {}).map((obj, index) => {
              const [attribute, ...variation] = obj.split("-");
              return (
                <p className="space-x-1 text-sm" key={index}>
                  <span className="uppercase font-medium">{attribute} : </span>
                  <span className="text-text-accent">
                    {variation.join(" ").toUpperCase()}
                  </span>
                </p>
              );
            })}
          </div>

          <p className="text-sm mb-2 font-semibold text-text-accent">
            {formatPrice({
              price: item.variation?.price || item.product.basePrice,
            })}
          </p>
        </div>
        <span
          className="p-1 cursor-pointer"
          onClick={() => onRemove(item, index)}
        >
          <X />
        </span>
      </div>
    </div>
  );
};

export default ItemSummary;
