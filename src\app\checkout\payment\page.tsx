"use client";

import { Wrapper } from "@/components/common/wrapper";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import store, { RootState, useAppDispatch } from "@/store/store";
import { CheckoutSteps } from "@/components/checkout/checkout-steps";
import { useRouter } from "next/navigation";
import {
  setCurrentStep,
  setPaymentDetails,
  setOrderNotes,
} from "@/store/slices/checkout-slice";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { PaymentMethod } from "@/interfaces";
import { CreditCard, Wallet, Truck, Tag } from "lucide-react";
import { OrderSummary } from "@/app/account/cart/order-summary";
import { Textarea } from "@/components/ui/textarea";

const PaymentPage = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const cart = useSelector((state: RootState) => state.cart);
  const { shippingAddress, billingAddress, orderNotes } = useSelector(
    (state: RootState) => state.checkout
  );

  const [paymentMethod, setPaymentMethod] =
    useState<PaymentMethod>("CreditCard");
  const [cardNumber, setCardNumber] = useState("");
  const [cardHolder, setCardHolder] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [saveCard, setSaveCard] = useState(false);
  const [notes, setNotes] = useState(orderNotes || "");

  useEffect(() => {
    dispatch(setCurrentStep(3));

    // Redirect if no shipping address is selected
    if (!shippingAddress || !billingAddress) {
      router.push("/checkout/address");
    }
  }, [dispatch, shippingAddress, billingAddress, router]);

  const handlePaymentMethodChange = (method: PaymentMethod) => {
    setPaymentMethod(method);
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNotes(e.target.value);
    dispatch(setOrderNotes(e.target.value));
  };

  const handleContinue = () => {
    // Save payment details to store
    dispatch(
      setPaymentDetails({
        method: paymentMethod,
        cardNumber: paymentMethod === "CreditCard" ? cardNumber : undefined,
        cardHolder: paymentMethod === "CreditCard" ? cardHolder : undefined,
        expiryDate: paymentMethod === "CreditCard" ? expiryDate : undefined,
        cvv: paymentMethod === "CreditCard" ? cvv : undefined,
        saveCard: paymentMethod === "CreditCard" ? saveCard : undefined,
      })
    );

    router.push("/checkout/confirmation");
  };

  const renderPaymentMethodCard = (
    method: PaymentMethod,
    title: string,
    description: string,
    icon: React.ReactNode
  ) => (
    <div
      className={`border rounded-md p-4 cursor-pointer ${
        paymentMethod === method ? "border-primary" : "border-gray-200"
      }`}
      onClick={() => handlePaymentMethodChange(method)}
    >
      <div className="flex items-center">
        <div
          className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
            paymentMethod === method ? "bg-primary text-white" : "bg-gray-100"
          }`}
        >
          {icon}
        </div>
        <div>
          <h3 className="font-medium">{title}</h3>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      </div>
    </div>
  );

  return (
    <Wrapper vertical>
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>
      <CheckoutSteps currentStep={3} />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-x-8 gap-y-7">
        <div className="lg:col-span-2 space-y-8">
          {/* Payment Methods Section */}
          <div className="border border-gray-200 rounded-md p-6">
            <h2 className="text-xl font-semibold mb-6">Payment Method</h2>

            <div className="space-y-4">
              {renderPaymentMethodCard(
                "CreditCard",
                "Credit / Debit Card",
                "Pay securely with your card",
                <CreditCard className="w-5 h-5" />
              )}

              {renderPaymentMethodCard(
                "paypal",
                "PayPal",
                "Fast and secure payment with PayPal",
                <Wallet className="w-5 h-5" />
              )}
            </div>

            {/* Credit Card Form */}
            {paymentMethod === "CreditCard" && (
              <div className="mt-6 space-y-4">
                <Input
                  type="number"
                  label="Card Number"
                  placeholder="1234 5678 9012 3456"
                  value={cardNumber}
                  onChange={(e) => setCardNumber(e.target.value)}
                />

                <Input
                  label="Card Holder Name"
                  placeholder="John Doe"
                  value={cardHolder}
                  onChange={(e) => setCardHolder(e.target.value)}
                />

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="Expiry Date"
                    placeholder="MM/YY"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(e.target.value)}
                  />

                  <Input
                    label="CVV"
                    placeholder="123"
                    type="password"
                    value={cvv}
                    onChange={(e) => setCvv(e.target.value)}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="save-card"
                    checked={saveCard}
                    onCheckedChange={(checked) => setSaveCard(!!checked)}
                  />
                  <label
                    htmlFor="save-card"
                    className="text-sm font-medium leading-none"
                  >
                    Save card for future payments
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* Order Notes Section */}
          <div className="border border-gray-200 rounded-md p-6">
            <h2 className="text-xl font-semibold mb-4">Order Notes</h2>
            <Textarea
              placeholder="Add any special instructions or notes about your order here..."
              className="min-h-[100px]"
              value={notes}
              onChange={handleNotesChange}
            />
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="border border-gray-200 rounded-md ">
            {/* Coupon Information */}
            {/* {cart.coupon && (
              <div className="mb-6 p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center">
                  <Tag className="w-4 h-4 mr-2 text-green-600" />
                  <h3 className="font-medium text-sm">Applied Coupon:</h3>
                </div>
                <div className="mt-1 pl-6">
                  <p className="text-sm font-medium">{cart.coupon.code}</p>
                  <p className="text-xs text-green-600">
                    {cart.coupon.discountType === "percentage"
                      ? `${cart.coupon.discountValue}% off`
                      : `${formatPrice({
                          price: cart.coupon.discountValue,
                        })} off`}
                  </p>
                  {cart.couponDiscount > 0 && (
                    <p className="text-sm text-green-600 font-medium mt-1">
                      Discount: {formatPrice({ price: cart.couponDiscount })}
                    </p>
                  )}
                </div>
              </div>
            )} */}

            <OrderSummary
              cart={cart}
              checkoutTitle="Complete Order"
              onCheckout={handleContinue}
              isCouponVisible={false}
              secondaryButtonOnClick={() => router.push("/checkout/address")}
              secondaryButtonTitle="Back to Address"
            />
          </div>
        </div>
      </div>
    </Wrapper>
  );
};

export default PaymentPage;
