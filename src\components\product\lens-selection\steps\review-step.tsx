'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { LensSelectionData } from '../lens-wizard';
import { formatPrice } from '@/utils/format-price';
import { IProduct } from '@/interfaces/product';
import { Check, Edit } from 'lucide-react';
import { IVariation } from '@/interfaces/variations';
import { PrescriptionStep } from './prescription-step';

interface ReviewStepProps {
  lensData: LensSelectionData;
  updateLensData: (data: Partial<LensSelectionData>, next?: boolean) => void;
  product: IProduct;
  variation: IVariation;
  onBack: () => void;
  onAddToCart: () => void;
}

export const ReviewStep: React.FC<ReviewStepProps> = ({
  lensData,
  updateLensData,
  product,
  variation,
  onBack,
  onAddToCart,
}) => {
  const [editPrescription, setEditPrescription] = useState(false);

  const handleEditComplete = () => {
    // Close the edit form and return to review view
    setEditPrescription(false);
  };

  const getVisionTypeName = () => {
    switch (lensData.visionType) {
      case 'distance':
        return 'Distance';
      case 'bifocal':
        return 'Bifocal & Varifocal';
      case 'reading':
        return 'Reading';
      case 'non-prescription':
        return 'Non Prescription';
      default:
        return '';
    }
  };

  const getLensCategoryName = () => {
    switch (lensData.lensCategory) {
      case 'clear':
        return 'Clear';
      case 'blue-light':
        return 'Blue Light Filtering';
      case 'sunglasses':
        return 'Sunglasses';
      case 'light-intelligent':
        return 'Light Intelligent';
      default:
        return '';
    }
  };
  return (
    <>
      {editPrescription ? (
        <PrescriptionStep
          lensData={lensData}
          updateLensData={updateLensData}
          onNext={handleEditComplete}
          onBack={() => setEditPrescription(false)}
          operation="edit"
        />
      ) : (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Review Your Selections</h2>

          {lensData.visionType !== 'non-prescription' && (
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-4">Prescription Details</h3>

              {lensData.prescription &&
              lensData.prescriptionType !== 'later' ? (
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium">
                      {lensData.prescription.name}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2"
                      onClick={() => setEditPrescription(true)}
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                  <div className="text-sm">
                    <div className="grid grid-cols-5 gap-2 mb-2 font-medium">
                      <div className="col-span-1"></div>
                      <div className="col-span-1">SPH</div>
                      <div className="col-span-1">CYL</div>
                      <div className="col-span-1">Axis</div>
                      <div className="col-span-1">PD</div>
                    </div>

                    <div className="grid grid-cols-5 gap-2 mb-2">
                      <div className="col-span-1">OD</div>
                      <div className="col-span-1">
                        {lensData?.prescription?.rightEye?.sph}
                      </div>
                      <div className="col-span-1">
                        {lensData?.prescription?.rightEye?.cyl}
                      </div>
                      <div className="col-span-1">
                        {lensData?.prescription?.rightEye?.axis}
                      </div>
                      <div className="col-span-1">
                        {lensData?.prescription?.pd}
                      </div>
                    </div>

                    <div className="grid grid-cols-5 gap-2">
                      <div className="col-span-1">OS</div>
                      <div className="col-span-1">
                        {lensData?.prescription?.leftEye?.sph}
                      </div>
                      <div className="col-span-1">
                        {lensData?.prescription?.leftEye?.cyl}
                      </div>
                      <div className="col-span-1">
                        {lensData?.prescription?.leftEye?.axis}
                      </div>
                      <div className="col-span-1"></div>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500">
                  {lensData.prescriptionType === 'later'
                    ? 'You will send your prescription after checkout.'
                    : 'No prescription details provided.'}
                </p>
              )}
            </div>
          )}

          <div className="border rounded-md p-4">
            <h3 className="font-medium mb-4">Order Summary</h3>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span>{product.name}</span>
                <span>
                  {formatPrice({ price: variation.price || product.basePrice })}
                </span>
              </div>

              <div className="flex justify-between">
                <span>{getVisionTypeName()}</span>
                <span>Free</span>
              </div>

              <div className="flex justify-between">
                <span>{getLensCategoryName()}</span>
                <span>{lensData.lensPackage.name}</span>
              </div>

              <div className="flex justify-between">
                <span></span>
                <span>
                  {lensData.lensPackage.price === 0
                    ? 'Free'
                    : formatPrice({ price: lensData.lensPackage.price })}
                </span>
              </div>

              <div className="border-t pt-3 flex justify-between font-semibold">
                <span>Subtotal</span>
                <span>{formatPrice({ price: lensData.totalPrice })}</span>
              </div>
            </div>

            <div className="mt-4 text-sm text-gray-500">
              <p>All orders include 120-day free returns.</p>
            </div>
          </div>

          <div className="flex justify-between mt-6">
            <Button variant="outline" onClick={onBack}>
              Back
            </Button>
            <Button
              onClick={onAddToCart}
              className="px-6 bg-primary hover:bg-primary/90"
            >
              Confirm and Add to Cart
            </Button>
          </div>
        </div>
      )}
    </>
  );
};
