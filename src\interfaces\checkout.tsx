import { IAddress } from "./address";
import { ICartItem } from "./cart";

export type PaymentMethod = "CreditCard" | "paypal" | "cash_on_delivery";

export interface PaymentDetails {
  method: PaymentMethod;
  cardNumber?: string;
  cardHolder?: string;
  expiryDate?: string;
  cvv?: string;
  saveCard?: boolean;
}

export interface CheckoutState {
  currentStep: number;
  shippingAddress: IAddress | null;
  billingAddress: IAddress | null;
  sameAsShipping: boolean;
  paymentDetails: PaymentDetails | null;
  orderNotes: string;
}
