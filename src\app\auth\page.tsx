"use client";

import { Tabs } from "@/components/ui/tabs";
import React, { Suspense, useState } from "react";
import LoginForm from "./_components/login";
import RegisterForm from "./_components/register";
import { NewsLetter } from "../(home)/_components/newsletter";
import { useSearchParams } from "next/navigation";

const Page = () => {
  const params = useSearchParams();
  const type = params.get("t") === "r" ? "register" : "login";
  const [active, setActive] = useState(type || "login");

  const tabItems = [
    { label: "Sign In", value: "login" },
    {
      label: "Sign Up",
      value: "register",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-center px-4">
        <div
          className={`w-full max-w-md sm:max-w-lg lg:max-w-xl space-y-6 bg-white rounded-md shadow pb-8 mt-28`}
        >
          <Tabs
            tabs={tabItems}
            activeTab={active}
            onChange={setActive}
            tabClassName="w-2/4 text-xl"
            innerClassName="justify-center"
          />

          <div className="px-6 sm:px-8">
            {active === "login" ? <LoginForm /> : <RegisterForm />}
          </div>
        </div>
      </div>

      <NewsLetter />
    </div>
  );
};

export default function AuthPage() {
  return (
    // You could have a loading skeleton as the `fallback` too
    <Suspense>
      <Page />
    </Suspense>
  );
}
