import React from "react";
import { Product } from "@/components/common/product";
import { Button } from "@/components/ui/button";

export const RunningSunglasses = () => {
  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-3xl font-semibold text-center font-workSans">
          Zunnies™ Running Sunglasses
        </h1>
      </div>
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {Array(10)
          .fill(0)
          .map((_, index) => (
            <Product key={index} />
          ))}
      </div>
      <div className="flex justify-center">
        <Button>Load more</Button>
      </div>
    </div>
  );
};
