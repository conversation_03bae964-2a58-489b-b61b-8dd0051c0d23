"use client";
import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { fetchEyeWears } from "@/store/slices/eye-wear-slice";
import { getImageUrl } from "@/utils/image-url";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";

export const EyeWearForEveryone = () => {
  const router = useRouter();
  const { eyeWears, loading } = useSelector(
    (state: RootState) => state.eyeWear
  );

  useEffect(() => {
    if (!eyeWears.length) {
      store.dispatch(fetchEyeWears());
    }
    return () => {};
  }, []);

  return (
    <div>
      <h3 className="font-semibold text-center text-2xl mb-6">
        Eyewear for Everyone
      </h3>
      <div className="gap-5 flex flex-col lg:flex-row">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-5 w-full">
          {loading &&
            [1, 2, 3, 4, 5, 6].map((_, index) => (
              <div
                key={`load-${index}`}
                className="flex flex-col items-center space-y-3"
              >
                <Skeleton className="w-24 h-14 " />
                <Skeleton className="w-24 h-5 " />
              </div>
            ))}
          {!loading &&
            eyeWears.slice(0, 6).map((eyewear, index) => (
              <Link
                href={`/product?eyeWear=${eyewear}`}
                key={index}
                className="flex flex-col items-center"
              >
                <Image
                  width={100}
                  height={100}
                  src={getImageUrl(eyewear.image)}
                  alt={eyewear.name}
                  className="h-12 object-contain"
                />
                <p>{eyewear.name}</p>
              </Link>
            ))}
        </div>
        <div className="flex items-center justify-center">
          <Button onClick={() => router.push("/eyewear")}>Show more</Button>
        </div>
      </div>
    </div>
  );
};
