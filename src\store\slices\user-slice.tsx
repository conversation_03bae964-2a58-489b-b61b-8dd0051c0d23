// REDUX
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

// INTERFACE
import { IUser, IUserSlice } from "@/interfaces";

// CONSTANTS
import { CHANGE_PASSWORD_URL, PROFILE_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";
import { getErrorMessage } from "@/utils/error-message";

const initialState: IUserSlice = {
  user: null,
  loading: true,
  success: "",
  error: "",
};

export const fetchProfile = createAsyncThunk<
  IUser,
  { token: string },
  FetchDataRejected
>("user/profile", async ({ token }, { rejectWithValue }) => {
  return api_interceptor
    .get(PROFILE_URL, { headers: { Authorization: token } })
    .then((res) => {
      return res.data.data;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Something went wrong"),
      });
    });
});

export const updateProfile = createAsyncThunk<
  { message: string; data: IUser },
  { fullName: string; email: string; phone: string },
  FetchDataRejected
>("user/update", async (data, { rejectWithValue }) => {
  return api_interceptor
    .put(PROFILE_URL, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Something went wrong"),
      });
    });
});

export const changePassword = createAsyncThunk<
  string,
  { oldPassword: string; newPassword: string },
  FetchDataRejected
>("user/change-password", async (data, { rejectWithValue }) => {
  return api_interceptor
    .put(CHANGE_PASSWORD_URL, data)
    .then((res) => {
      return res.data.message;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Something went wrong"),
      });
    });
});

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    // provide email and name
    setUser: (state, action: PayloadAction<IUser>) => {
      state.user = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchProfile.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchProfile.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.user = payload;
    });
    builder.addCase(fetchProfile.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
    builder.addCase(changePassword.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(changePassword.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.success = payload;
    });
    builder.addCase(changePassword.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
    builder.addCase(updateProfile.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(updateProfile.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.user = payload.data;
      state.success = payload.message;
    });
    builder.addCase(updateProfile.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export const { setUser } = userSlice.actions;
export default userSlice.reducer;
