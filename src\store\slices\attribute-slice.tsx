// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IAttributeSlice, IAttributeWithValues } from "@/interfaces";

// CONSTANTS
import { ATTRIBUTE_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: IAttributeSlice = {
  attributes: [],
  loading: true,
  error: "",
};

export const fetchAttributes = createAsyncThunk<
  IAttributeWithValues[],
  void,
  FetchDataRejected
>("attributes/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(ATTRIBUTE_URL)
    .then((res) => {
      return res.data.data;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const attributesSlice = createSlice({
  name: "attribute",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchAttributes.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchAttributes.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.attributes = payload;
    });
    builder.addCase(fetchAttributes.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default attributesSlice.reducer;
