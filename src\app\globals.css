@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 38 5% 0%;
    --card: 38 50% 90%;
    --card-foreground: 38 5% 10%;
    --popover: 38 100% 95%;
    --popover-foreground: 38 100% 0%;
    --primary: 38 82.8% 43.3%;
    --primary-foreground: 0 0% 100%;
    --secondary: 38 30% 70%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 30% 85%;
    --muted-foreground: 38 5% 35%;
    --accent: 35 48% 86%;
    --accent-foreground: 38 5% 10%;
    --destructive: 0 100% 30%;
    --destructive-foreground: 38 5% 90%;
    --border: 38 30% 50%;
    --input: 38 30% 18%;
    --ring: 38 82.8% 43.3%;
    --radius: 0.5rem;
    --link-gray: 198 9% 41%;
    --text-accent: 200, 10%, 41%;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.react-phone-input__input {
  padding-left: 3rem !important;
}

.flag-dropdown {
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1)) !important;
}

.selected-flag {
  background: white;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1)) !important;
}

body {
  background: white;
}

.description-text {
  color: rgb(128, 128, 128) !important;
}

@layer utilities {
  @keyframes pulse-slow {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  .animate-pulse-slow {
    animation: pulse-slow 2s infinite;
  }
}
