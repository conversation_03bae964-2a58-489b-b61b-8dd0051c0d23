"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
  buttonClassName?: string;
  siblingCount?: number; // number of pages to show beside current
}

const range = (start: number, end: number): number[] => {
  return Array.from({ length: end - start + 1 }, (_, i) => start + i);
};

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className,
  buttonClassName,
  siblingCount = 1,
}) => {
  const DOTS = "...";

  const paginationRange = React.useMemo(() => {
    const totalPageNumbers = siblingCount * 2 + 5;

    if (totalPages <= totalPageNumbers) {
      return range(1, totalPages);
    }

    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);

    const showLeftDots = leftSiblingIndex > 2;
    const showRightDots = rightSiblingIndex < totalPages - 2;

    const firstPageIndex = 1;
    const lastPageIndex = totalPages;

    if (!showLeftDots && showRightDots) {
      const leftRange = range(1, 3 + 2 * siblingCount);
      return [...leftRange, DOTS, totalPages];
    }

    if (showLeftDots && !showRightDots) {
      const rightRange = range(totalPages - (2 * siblingCount + 2), totalPages);
      return [firstPageIndex, DOTS, ...rightRange];
    }

    if (showLeftDots && showRightDots) {
      const middleRange = range(leftSiblingIndex, rightSiblingIndex);
      return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex];
    }
  }, [currentPage, totalPages, siblingCount]);

  const handleClick = (page: number) => {
    if (page !== currentPage && typeof page === "number") {
      onPageChange(page);
    }
  };

  return (
    <nav className={cn("flex items-center gap-2", className)}>
      <button
        onClick={() => handleClick(currentPage - 1)}
        disabled={currentPage === 1}
        className={cn(
          "h-9 w-9 flex items-center justify-center rounded-md border text-sm hover:bg-accent disabled:opacity-50",
          buttonClassName
        )}
      >
        <ChevronLeft className="h-4 w-4" />
      </button>

      {paginationRange?.map((page, index) => {
        if (page === DOTS) {
          return (
            <span
              key={index}
              className="h-9 w-9 flex items-center justify-center text-muted-foreground text-sm"
            >
              ...
            </span>
          );
        }

        return (
          <button
            key={index}
            onClick={() => handleClick(Number(page))}
            className={cn(
              "h-9 w-9 flex items-center justify-center rounded-md border text-sm",
              buttonClassName,
              page === currentPage
                ? "bg-primary text-primary-foreground"
                : "hover:bg-accent"
            )}
          >
            {page}
          </button>
        );
      })}

      <button
        onClick={() => handleClick(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={cn(
          "h-9 w-9 flex items-center justify-center rounded-md border text-sm hover:bg-accent disabled:opacity-50",
          buttonClassName
        )}
      >
        <ChevronRight className="h-4 w-4" />
      </button>
    </nav>
  );
};
