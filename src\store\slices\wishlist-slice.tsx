// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IWishlistSlice, IWishlist } from "@/interfaces";

// CONSTANTS
import { WISHLIST_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";
import { getErrorMessage } from "@/utils/error-message";

const initialState: IWishlistSlice = {
  wishlist: [],
  loading: true,
  error: "",
};

export const addToWishlist = createAsyncThunk<
  IWishlist,
  { productId: string },
  FetchDataRejected
>("wishlist/add", async (payload, { rejectWithValue }) => {
  return api_interceptor
    .post(WISHLIST_URL, payload)
    .then((res) => {
      return res.data.data;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Something went wrong"),
      });
    });
});

export const removeFromWishlist = createAsyncThunk<
  { productId: string },
  { productId: string },
  FetchDataRejected
>("wishlist/remove", async (payload, { rejectWithValue }) => {
  return api_interceptor
    .delete(WISHLIST_URL + "/clear/" + payload.productId)
    .then((res) => {
      return payload;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err.message, "Something went wrong"),
      });
    });
});

export const fetchWishlist = createAsyncThunk<
  IWishlist[],
  { token?: string },
  FetchDataRejected
>("wishlist/fetch", async ({ token }, { rejectWithValue }) => {
  // add token in header if token is provided and don't remove earlier header if we have
  const headers = token ? { Authorization: token } : {};

  return api_interceptor
    .get(WISHLIST_URL, { headers })
    .then((res) => {
      return res.data.data;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const wishlistSlice = createSlice({
  name: "wishlist",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchWishlist.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchWishlist.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.wishlist = payload;
    });
    builder.addCase(fetchWishlist.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
    builder.addCase(addToWishlist.pending, (state) => {
      state.error = "";
      state.loading = true;
    });
    builder.addCase(addToWishlist.fulfilled, (state, { payload }) => {
      state.wishlist.push(payload);
      state.loading = false;
    });
    builder.addCase(addToWishlist.rejected, (state, { payload }) => {
      state.error = payload?.message || "Something went wrong";
      state.loading = false;
    });
    builder.addCase(removeFromWishlist.pending, (state) => {
      state.error = "";
      state.loading = true;
    });
    builder.addCase(removeFromWishlist.fulfilled, (state, { payload }) => {
      state.wishlist = state.wishlist.filter(
        (item) => item.product._id !== payload.productId
      );
      state.loading = false;
    });
    builder.addCase(removeFromWishlist.rejected, (state, { payload }) => {
      state.error = payload?.message || "Something went wrong";
      state.loading = false;
    });
  },
});

export default wishlistSlice.reducer;
