"use client";
import {
  Carousel,
  CarouselContent,
  Carousel<PERSON>tem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import Image from "next/image";
import React from "react";

export const FanFavorites = () => {
  const fanFavorites = [
    "/fans-favorite/1.svg",
    "/fans-favorite/2.svg",
    "/fans-favorite/3.svg",
    "/fans-favorite/4.svg",
    "/fans-favorite/5.svg",
  ];
  const plugin = React.useRef(
    Autoplay({ delay: 2000, stopOnInteraction: true })
  );

  return (
    <div className="space-y-3">
      <p className="text-3xl font-bold text-center">Fan Favorites</p>
      <p className="text-center text-gray-400">
        Tag us on social with{" "}
        <span className="text-primary">#lovemyzennis</span> for a chance to be
        featured here.
      </p>
      <div className="h-2" />

      <Carousel
        plugins={[plugin.current]}
        className="w-full h-max"
        onMouseEnter={plugin.current.stop}
        onMouseLeave={plugin.current.reset}
      >
        <CarouselContent className="w-full h-max -ml-4">
          {fanFavorites.map((fanFav, index) => (
            <CarouselItem
              key={index}
              className="basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5 pl-4"
            >
              <Image
                src={fanFav}
                alt="fan favorite"
                width={1500}
                height={100}
                className="w-full h-auto"
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </div>
  );
};
