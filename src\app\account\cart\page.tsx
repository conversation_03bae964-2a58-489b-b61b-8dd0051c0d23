"use client";

import { But<PERSON> } from "@/components/ui/button";
import QuantityInput from "@/components/ui/quantity-input";
import { Separator } from "@/components/ui/separator";
import { formatPrice } from "@/utils/format-price";
import { MoveRight, Trash2 } from "lucide-react";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { OrderSummary } from "./order-summary";
import { useSelector } from "react-redux";
import store, { RootState, useAppDispatch } from "@/store/store";
import {
  fetchCart,
  removeFromCart,
  revalidateCoupon,
  updateQuantity,
} from "@/store/slices/cart-slice";
import { ICartItem } from "@/interfaces";
import { getImageUrl } from "@/utils/image-url";
import { useRouter } from "next/navigation";

const Page = () => {
  const router = useRouter();
  const cart = useSelector((state: RootState) => state.cart);

  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(fetchCart());
    const intervalId = setInterval(() => {
      dispatch(fetchCart());
    }, 1000);
    return () => clearInterval(intervalId);
  }, [dispatch]);

  useEffect(() => {
    store.dispatch(revalidateCoupon());
    return () => {};
  }, [cart.cart.length]);

  const setQuantity = (newQuantity: number, item: ICartItem) => {
    dispatch(updateQuantity({ id: item.id, quantity: newQuantity }));
    // Force a re-render by dispatching fetchCart
    setTimeout(() => {
      dispatch(fetchCart());
    }, 100);
  };

  const removeItem = (item: ICartItem) => {
    dispatch(removeFromCart(item.id));
    // Force a re-render by dispatching fetchCart
    setTimeout(() => {
      dispatch(fetchCart());
    }, 100);
  };

  const onCheckout = () => {
    router.push("/checkout");
  };

  return (
    <div className="py-7 space-y-6">
      <h2 className="font-semibold text-2xl">Your Cart</h2>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-x-5 gap-y-7">
        <div className="col-span-2 border border-gray-200 rounded-sm px-6 py-5 h-fit">
          {cart?.cart?.map((item: ICartItem, index: number) => {
            return (
              <div key={"cart-item-" + index}>
                <div className={`flex items-center w-100`}>
                  <div className="bg-gray-100 w-32 h-32 rounded-sm mr-4 flex justify-center items-center">
                    <img
                      className="h-auto py-6 px-2"
                      src={getImageUrl(item.product.thumbnail!)}
                      alt=""
                    />
                  </div>

                  <div className="w-full">
                    <div className="flex justify-between items-center">
                      <p className="font-semibold">
                        <Link href={`/product/${item.product.slug}`}>
                          {item.product.name}
                        </Link>
                      </p>

                      <Button
                        size={"icon"}
                        className="bg-white shadow-none hover:bg-white hover:shadow-none text-red-500"
                        onClick={() => removeItem(item)}
                      >
                        <Trash2 />
                      </Button>
                    </div>

                    <div className="mb-7">
                      {/* Display variation attributes */}
                      {Object.keys(item.variation?.attributes || {}).map(
                        (obj) => {
                          const [attribute, ...variation] = obj.split("-");
                          return (
                            <p key={obj} className="space-x-1 text-sm">
                              <span className="capitalize font-medium">
                                {attribute} :{" "}
                              </span>
                              <span className="text-text-accent capitalize">
                                {variation.join("-")}
                              </span>
                            </p>
                          );
                        }
                      )}

                      {/* Display lens information if present */}
                      {item.lensData && (
                        <div className="mt-2 p-2 bg-gray-50 rounded-md">
                          <p className="text-sm font-medium">Custom Lenses:</p>
                          <div className="text-xs space-y-1 mt-1">
                            <p>
                              <span className="font-medium">Vision Type:</span>{" "}
                              {item.lensData.visionType === "distance"
                                ? "Distance"
                                : item.lensData.visionType === "bifocal"
                                ? "Bifocal & Varifocal"
                                : item.lensData.visionType === "reading"
                                ? "Reading"
                                : "Non Prescription"}
                            </p>
                            <p>
                              <span className="font-medium">Lens Type:</span>{" "}
                              {item.lensData.lensCategory}
                            </p>
                            <p>
                              <span className="font-medium">Package:</span>{" "}
                              {item.lensData.lensPackage.name}
                              {item.lensData.lensPackage.price > 0 && (
                                <span className="text-primary ml-1">
                                  (+
                                  {formatPrice({
                                    price:
                                      item.lensData.lensPackage.price +
                                      (item.lensData.lensPackage.subPackage
                                        ?.price || 0),
                                  })}
                                  )
                                </span>
                              )}
                              {item.lensData.lensPackage.subPackage?.title &&
                                `(+${item.lensData.lensPackage.subPackage.price})`}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-between items-center font-semibold text-xl">
                      <span>
                        {formatPrice({
                          price:
                            ((item.variation?.price || item.product.basePrice) +
                              (item.lensData?.lensPackage?.price || 0)) *
                            item.quantity,
                        })}
                      </span>

                      <QuantityInput
                        quantity={item.quantity}
                        onChange={($event) => setQuantity($event, item)}
                        containerClass="rounded-full border-none bg-gray-100"
                        buttonClass="rounded-full cursor-pointer hover:bg-gray-300"
                      />
                    </div>
                  </div>
                </div>

                {index !== cart.cart?.length - 1 ? <Separator /> : ""}
              </div>
            );
          })}

          {!cart?.cart?.length ? (
            <div className="flex flex-col justify-center items-center">
              <p className="text-center mb-2">Cart is empty</p>

              <Link
                href={"/product"}
                className="flex items-center text-primary"
              >
                <span className="mr-1">Browse Products</span>
                <MoveRight />
              </Link>
            </div>
          ) : (
            ""
          )}
        </div>

        <OrderSummary cart={cart} onCheckout={onCheckout} />
      </div>
    </div>
  );
};

export default Page;
