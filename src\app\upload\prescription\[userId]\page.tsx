"use client"
import React, { use<PERSON>em<PERSON>, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useParams } from "next/navigation";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const prescriptionValidationSchema = z.object({
  name: z.string().min(1, "Prescription name is required"),
  rightEye: z.object({
    sph: z.string().min(1, "SPH is required"),
    cyl: z.string().optional(),
    axis: z.string().optional(),
    add: z.string().optional(),
  }),
  leftEye: z.object({
    sph: z.string().min(1, "SPH is required"),
    cyl: z.string().optional(),
    axis: z.string().optional(),
    add: z.string().optional(),
  }),
  pd: z.string().optional(),
});

type PrescriptionFormData = z.infer<typeof prescriptionValidationSchema>;

const UploadPrescription: React.FC = () => {
  const params = useParams();
  const userId = params.userId as string;

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<PrescriptionFormData>({
    resolver: zodResolver(prescriptionValidationSchema),
    defaultValues: {
      name: "",
      rightEye: { sph: "", cyl: "", axis: "", add: "" },
      leftEye: { sph: "", cyl: "", axis: "", add: "" },
      pd: "",
    },
  });

  const sphValues = useMemo(() => {
    const values = [];
    for (let i = -9.75; i <= 9.75; i += 0.25) {
      const sphValue = i.toFixed(2);
      values.push({ label: sphValue, value: sphValue });
      if (sphValue === "0.00") {
        values.push({ label: "Plano", value: "Plano" });
      }
    }
    return values;
  }, []);

  const cylValues = useMemo(() => {
    const values = [];
    for (let i = -3.0; i <= 3.0; i += 0.25) {
      values.push({ label: i.toFixed(2), value: i.toFixed(2) });
    }
    return values;
  }, []);

  const addValues = useMemo(() => {
    const values = [{ label: "n/a", value: "" }];
    for (let i = -4.0; i <= 4.0; i += 0.25) {
      values.push({ label: i.toFixed(2), value: i.toFixed(2) });
    }
    return values;
  }, []);

  const onSubmit = async (data: PrescriptionFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const response = await fetch(`http://localhost:8000/api/v1/prescription/${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || 'Failed to submit prescription');
      }

      setSubmitStatus('success');
      reset(); // Reset form after successful submission
    } catch (error) {
      console.error('Error submitting prescription:', error);
      setSubmitStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center">
      <div className="w-full max-w-2xl px-4">
        {submitStatus === 'success' && (
          <Alert  className="mb-4">
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>
              Your prescription has been successfully submitted.
            </AlertDescription>
          </Alert>
        )}

        {submitStatus === 'error' && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {errorMessage || 'Failed to submit prescription. Please try again.'}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="mt-6 border rounded-md p-4 flex flex-col">
          <h3 className="font-medium mb-4">Enter Prescription Details</h3>

          <div className="mb-4">
            <Label htmlFor="prescription-name">Prescription Name</Label>
            <Input
              id="prescription-name"
              {...register("name")}
              className="mt-1"
            />
            {errors.name && (
              <p className="text-sm text-red-500 mt-1">
                {errors.name.message}
              </p>
            )}
          </div>

          {/* Grid Headers */}
          <div className="grid grid-cols-5 gap-2 mb-2 text-sm font-medium">
            <div className="col-span-1"></div>
            <div className="col-span-1">SPH</div>
            <div className="col-span-1">CYL</div>
            <div className="col-span-1">Axis</div>
            <div className="col-span-1">ADD</div>
          </div>

          {/* Right Eye */}
          <div className="grid grid-cols-5 gap-2 mb-4">
            <div className="col-span-1 flex items-center"><Label>OD (Right)</Label></div>
            <Select {...register("rightEye.sph")} options={sphValues} />
            <Select {...register("rightEye.cyl")} options={cylValues} />
            <Input {...register("rightEye.axis")} placeholder="0" />
            <Select {...register("rightEye.add")} options={addValues} />
          </div>

          {/* Left Eye */}
          <div className="grid grid-cols-5 gap-2 mb-4">
            <div className="col-span-1 flex items-center"><Label>OS (Left)</Label></div>
            <Select {...register("leftEye.sph")} options={sphValues} />
            <Select {...register("leftEye.cyl")} options={cylValues} />
            <Input {...register("leftEye.axis")} placeholder="0" />
            <Select {...register("leftEye.add")} options={addValues} />
          </div>

          {/* PD */}
          <div className="grid grid-cols-5 gap-2 mb-4">
            <div className="col-span-1 flex items-center"><Label>PD</Label></div>
            <Input {...register("pd")} placeholder="63" />
          </div>

          <Button
            type="submit"
            className="self-end"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              'Submit'
            )}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default UploadPrescription;
