"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LensSelectionData } from "../lens-wizard";
// import { formatPrice } from "@/utils/format-price";
// import { Check } from "lucide-react";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
// import Image from "next/image";
// import { getImageUrl } from "@/utils/image-url";
import { LensesPackage } from "../lenses-package";
import { ILensPackage, ILensSubPackage } from "@/interfaces";

interface LensPackageStepProps {
  lensData: LensSelectionData;
  updateLensData: (data: Partial<LensSelectionData>) => void;
  onNext: () => void;
  onBack: () => void;
}

export const LensPackageStep: React.FC<LensPackageStepProps> = ({
  lensData,
  updateLensData,
  onNext,
  onBack,
}) => {
  const { lensPackages } = useSelector((state: RootState) => state.lensPackage);
  const filteredLensPackages = lensPackages.filter(
    (lensPackage) => lensPackage.lensCategory.name === lensData.lensCategory
  );

  const handleSelect = (
    newPackage: ILensPackage,
    subPackage?: ILensSubPackage | null
  ) => {
    updateLensData({
      lensPackage: {
        name: newPackage.name,
        price: newPackage.price,
        _id: newPackage._id,
        subPackage: {
          price: subPackage?.price || 0,
          title: subPackage?.title || "",
        },
      },
    });
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Lens Package</h2>
      <div className="space-y-4">
        {filteredLensPackages.map((pkg) => (
          <LensesPackage
            data={pkg}
            handleSelect={handleSelect}
            packageId={lensData.lensPackage._id}
            subPackage={lensData.lensPackage.subPackage?.title}
            selected={lensData.lensPackage._id === pkg._id}
            key={pkg._id}
          />
        ))}
      </div>
      <div className="flex justify-between mt-6">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onNext} disabled={!lensData.lensPackage?.name}>
          Next
        </Button>
      </div>
    </div>
  );
};
