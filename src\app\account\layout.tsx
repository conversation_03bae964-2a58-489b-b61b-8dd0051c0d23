"use client";

import {
  Heart,
  Layers,
  LogOut,
  MapPin,
  NotebookText,
  Settings,
  ShoppingCart,
  Store,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { NewsLetter } from "../(home)/_components/newsletter";
import { useEffect, useState } from "react";
import { Wrapper } from "@/components/common/wrapper";
import { signOut } from "next-auth/react";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [mounted, setMounted] = useState(false);

  const tabs = [
    {
      label: "Dashboard",
      href: "/account/dashboard",
      icon: Layers,
    },
    {
      label: "Order History",
      href: "/account/orders",
      icon: Store,
    },
    {
      label: "Track Order",
      href: "/account/track-order",
      icon: MapPin,
    },
    {
      label: "Shopping Cart",
      href: "/account/cart",
      icon: ShoppingCart,
    },
    {
      label: "Wishlist",
      href: "/account/wishlist",
      icon: Heart,
    },
    {
      label: "Address",
      href: "/account/address",
      icon: NotebookText,
    },
    {
      label: "Setting",
      href: "/account/setting",
      icon: Settings,
    },
    {
      label: "Change Password",
      href: "/account/change-password",
      icon: Settings,
    },
    {
      label: "Logout",
      type: "LOGOUT",
      icon: LogOut,
    },
  ];

  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const logout = () => {
    signOut({ callbackUrl: "/" });
  };

  return (
    <div className="space-y-6">
      <Wrapper
        vertical
        className="grid grid-cols-1 gap-5 md:gap-8 lg:gap-12 xl:gap-20 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
      >
        <div className="shadow h-fit md:sticky md:top-12 bg-white z-10">
          <ul className="py-3">
            {tabs?.map((tab, index) => {
              return (
                <li key={"tab" + index}>
                  {tab?.href ? (
                    <Link
                      href={tab.href}
                      className={`flex text-sm px-6 py-3 cursor-pointer transition-all duration-100 text-link-gray hover:bg-primary hover:text-white items-center ${
                        pathname === tab.href ||
                        (pathname.startsWith(tab.href) &&
                          tab.href !== "/account/dashboard")
                          ? "bg-primary text-white"
                          : ""
                      }`}
                    >
                      <tab.icon size={18} className="mr-3" />
                      {tab.label}
                    </Link>
                  ) : (
                    <span
                      onClick={logout}
                      className={`flex text-sm px-6 py-2.5 cursor-pointer transition-all duration-100 text-link-gray hover:bg-primary hover:text-white items-center`}
                    >
                      <tab.icon size={18} className="mr-3" />
                      {tab.label}
                    </span>
                  )}
                </li>
              );
            })}
          </ul>
        </div>

        <div className="col-span-1 sm:col-span-1 md:col-span-2 lg:col-span-3 xl:col-span-4">
          {children}
        </div>
      </Wrapper>

      <NewsLetter />
    </div>
  );
}
