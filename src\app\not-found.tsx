"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Home, MoveLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";
import { NewsLetter } from "./(home)/_components/newsletter";

const NotFound = () => {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="mx-auto flex justify-center flex-col items-center mb-24">
        <img src="/404.png" className="w-96" alt="" />

        <h2 className="text-5xl font-bold text-center mb-4">404</h2>

        <p className="text-2xl uppercase mb-6">Page Not Found</p>

        <div className="grid grid-cols-2 gap-4">
          <Button
            className="uppercase hover:text-primary hover:bg-white hover:border hover:border-primary"
            onClick={() => router.back()}
          >
            <MoveLeft />
            Go Back
          </Button>

          <Button
            className="uppercase bg-transparent border border-primary text-primary hover:text-white"
            onClick={() => router.replace("/")}
          >
            <Home />
            Go To Home
          </Button>
        </div>
      </div>

      <NewsLetter />
    </div>
  );
};

export default NotFound;
