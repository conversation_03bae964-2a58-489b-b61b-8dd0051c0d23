// 1. Install required packages:
// npm install three @react-three/fiber @react-three/drei @mediapipe/face_mesh

// pages/index.tsx

"use client";

import React, { useRef, useEffect, useState, Suspense } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import { OrbitControls, useGLTF } from "@react-three/drei";
import * as THREE from "three";
import * as faceMeshModule from "@mediapipe/face_mesh";

const Sunglasses = ({ position, rotation, scale }: any) => {
  const { scene } = useGLTF("/sunglass/1.glb");
  const ref = useRef<THREE.Group>(null);

  useFrame(() => {
    if (ref.current) {
      ref.current.position.copy(position);
      ref.current.rotation.copy(rotation);
    }
  });

  return (
    <group ref={ref} scale={scale}>
      <primitive object={scene} position={[0, -0.25, 0.05]} />
    </group>
  );
};

const WebcamFaceTracker = ({ onFaceData, videoRef }: any) => {
  useEffect(() => {
    const faceMesh = new faceMeshModule.FaceMesh({
      locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.5.1635989137/${file}`,
    });

    faceMesh.setOptions({
      maxNumFaces: 1,
      refineLandmarks: true,
      minDetectionConfidence: 0.7,
      minTrackingConfidence: 0.7,
    });

    faceMesh.onResults((results) => {
      if (results?.multiFaceLandmarks?.[0]) {
        onFaceData(results.multiFaceLandmarks[0]);
      }
    });

    const setupCamera = async () => {
      if (videoRef.current) {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: "user", width: 640, height: 480 },
        });
        videoRef.current.srcObject = stream;

        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play();
          const processFrame = async () => {
            if (videoRef.current?.readyState === 4) {
              try {
                await faceMesh.send({ image: videoRef.current });
              } catch (error) {
                console.warn("FaceMesh send error:", error);
              }
            }
            requestAnimationFrame(processFrame);
          };
          requestAnimationFrame(processFrame);
        };
      }
    };

    setupCamera();
  }, [onFaceData, videoRef]);

  return null;
};

const Home = () => {
  const [glassesPos, setGlassesPos] = useState(new THREE.Vector3(0, 0, 0));
  const [glassesRot, setGlassesRot] = useState(new THREE.Euler(0, 0, 0));
  const [glassesScale, setGlassesScale] = useState(1);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handleFaceLandmarks = (landmarks: any) => {
    const leftEye = landmarks[33];
    const rightEye = landmarks[263];

    const eyeMidX = (leftEye.x + rightEye.x) / 2;
    const eyeMidY = (leftEye.y + rightEye.y) / 2;
    const eyeMidZ = (leftEye.z + rightEye.z) / 2;

    const dx = rightEye.x - leftEye.x;
    const dy = rightEye.y - leftEye.y;
    const dz = rightEye.z - leftEye.z;
    const angle = Math.atan2(dy, dx);

    const eyeDist = Math.sqrt(dx * dx + dy * dy + dz * dz);
    const scaleFactor = eyeDist * 25;

    const posX = eyeMidX * 2 - 1;
    const posY = -eyeMidY * 2 + 1;
    const posZ = -eyeMidZ * 2.5;

    setGlassesPos(new THREE.Vector3(posX, posY, posZ));
    setGlassesRot(new THREE.Euler(0, 0, -angle));
    setGlassesScale(scaleFactor);
  };

  return (
    <div style={{ position: "relative", height: "100vh", width: "100vw", overflow: "hidden", backgroundColor: "#000" }}>
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        style={{
          position: "absolute",
          width: "100%",
          height: "100%",
          objectFit: "cover",
          zIndex: 1,
        }}
      />

      <WebcamFaceTracker onFaceData={handleFaceLandmarks} videoRef={videoRef} />

      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 2,
          pointerEvents: "none",
          background: "transparent",
        }}
      >
        <Canvas
          camera={{ position: [0, 0, 2.5] }}
          gl={{ alpha: true, antialias: true }}
          style={{ background: "transparent" }}
        >
          <ambientLight intensity={0.7} />
          <Suspense fallback={null}>
            <Sunglasses position={glassesPos} rotation={glassesRot} scale={glassesScale} />
          </Suspense>
        </Canvas>
      </div>
    </div>
  );
};

export default Home;

// Notes:
// - Uses MediaPipe face_mesh version 0.5.1635989137 to ensure proper asset availability
// - Fully replaces MediaPipe's Camera with getUserMedia + requestAnimationFrame
// - Prevents WASM crashes and flicker by waiting for video readiness
// - Uses defensive coding to catch .send() timing issues
