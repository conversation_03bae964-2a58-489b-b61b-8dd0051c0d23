"use client";

import React, { useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState, useAppDispatch } from "@/store/store";
import { fetchOrderById } from "@/store/slices/order-slice";
import { Wrapper } from "@/components/common/wrapper";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Loader } from "lucide-react";
import { formatPrice } from "@/utils/format-price";
import { IMAGE_BASE_URL } from "@/constants/url";
import { toast } from "sonner";

export default function OrderDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { currentOrder, loading, error } = useSelector(
    (state: RootState) => state.order
  );
  const orderId = params.id as string;

  useEffect(() => {
    if (orderId) {
      loadOrderDetails();
    }
  }, [orderId, dispatch]);

  const loadOrderDetails = () => {
    dispatch(fetchOrderById(orderId))
      .unwrap()
      .catch((error) => {
        toast.error(error.message || "Failed to load order details");
        router.push("/account/orders");
      });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <Wrapper vertical>
        <div className="flex justify-center items-center py-20">
          <Loader className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2">Loading order details...</span>
        </div>
      </Wrapper>
    );
  }

  if (error) {
    return (
      <Wrapper vertical>
        <div className="text-center py-10">
          <p className="text-red-500">{error}</p>
          <div className="mt-4 space-x-4">
            <Button onClick={loadOrderDetails}>Try Again</Button>
            <Button
              variant="outline"
              onClick={() => router.push("/account/orders")}
            >
              Back to Orders
            </Button>
          </div>
        </div>
      </Wrapper>
    );
  }

  if (!currentOrder) {
    return (
      <Wrapper vertical>
        <div className="text-center py-10">
          <p>Order not found</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push("/account/orders")}
          >
            Back to Orders
          </Button>
        </div>
      </Wrapper>
    );
  }

  return (
    <Wrapper vertical>
      <div className="mb-6">
        <Button
          variant="ghost"
          className="flex items-center text-gray-600 hover:text-black"
          onClick={() => router.push("/account/orders")}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Orders
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold">
          Order #{currentOrder.orderId || currentOrder._id}
        </h1>
        <p className="text-gray-500 mt-1">
          Placed on {formatDate(currentOrder.date)}
        </p>
        <div className="mt-2">
          <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            {currentOrder.status}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="border border-gray-200 rounded-md p-4">
          <h2 className="font-semibold mb-3">Order Summary</h2>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500">Subtotal:</span>
              <span>{formatPrice({ price: currentOrder.subtotal || 0 })}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Shipping:</span>
              <span>
                {formatPrice({ price: currentOrder.shippingCost || 0 })}
              </span>
            </div>
            {currentOrder.tax && (
              <div className="flex justify-between">
                <span className="text-gray-500">Tax:</span>
                <span>{formatPrice({ price: currentOrder.tax })}</span>
              </div>
            )}
            {currentOrder.discount && (
              <div className="flex justify-between">
                <span className="text-gray-500">Discount:</span>
                <span>-{formatPrice({ price: currentOrder.discount })}</span>
              </div>
            )}
            <div className="flex justify-between font-semibold border-t border-gray-200 pt-2 mt-2">
              <span>Total:</span>
              <span>
                {formatPrice({ price: currentOrder.totalAmount || 0 })}
              </span>
            </div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-md p-4">
          <h2 className="font-semibold mb-3">Shipping Information</h2>
          {currentOrder.shippingAddress ? (
            <div className="text-sm">
              <p className="font-medium">
                {currentOrder.shippingAddress.fullName}
              </p>
              <p>{currentOrder.shippingAddress.address}</p>
              <p>
                {currentOrder.shippingAddress.city},{" "}
                {currentOrder.shippingAddress.state}{" "}
                {currentOrder.shippingAddress.postalCode}
              </p>
              <p>{currentOrder.shippingAddress.country}</p>
              <p className="mt-1">
                Phone: {currentOrder.shippingAddress.phone}
              </p>
            </div>
          ) : (
            <p className="text-sm text-gray-500">
              No shipping address available
            </p>
          )}
        </div>
      </div>

      <div className="border border-gray-200 rounded-md p-4 mb-8">
        <h2 className="font-semibold mb-4">Order Items</h2>
        <div className="space-y-4">
          {currentOrder.items.map((item: any, index: number) => (
            <div
              key={index}
              className="flex flex-col md:flex-row border-b border-gray-200 pb-4 mb-4 last:border-0 last:pb-0 last:mb-0"
            >
              <div className="flex-shrink-0 w-20 h-20 bg-gray-100 rounded-md overflow-hidden mr-4 mb-4 md:mb-0">
                <img
                  src={
                    item.product?.thumbnail
                      ? `${IMAGE_BASE_URL}${item.product.thumbnail}`
                      : "/sunglass.png"
                  }
                  alt={item.product?.name || "Product"}
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="flex-grow">
                <h3 className="font-medium">
                  {item.product?.name || "Product"}
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  {item.product?.shortDescription || ""}
                </p>
                <div className="flex justify-between mt-2">
                  <span className="text-sm">Quantity: {item.quantity}</span>
                  <span className="font-medium">
                    {formatPrice({
                      price:
                        item.amount ||
                        (item.product?.basePrice || 0) * item.quantity,
                    })}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Wrapper>
  );
}
