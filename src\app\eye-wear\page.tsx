"use client";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { fetchEyeWears } from "@/store/slices/eye-wear-slice";
import { getImageUrl } from "@/utils/image-url";
import { Wrapper } from "@/components/common/wrapper";

const EyeWearForEveryone = () => {
  const { eyeWears, loading } = useSelector(
    (state: RootState) => state.eyeWear
  );

  useEffect(() => {
    if (!eyeWears.length) {
      store.dispatch(fetchEyeWears());
    }
    return () => {};
  }, []);

  return (
    <Wrapper vertical className="">
      <h3 className="font-semibold text-2xl mb-6 text-center">
        Eyewear for Everyone
      </h3>
      <div className="gap-5 flex flex-col lg:flex-row">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-5 w-full">
          {eyeWears.map((eyewear, index) => (
            <Link
              href={`/product?eyeWear=${eyewear.slug}`}
              key={index}
              className="flex flex-col items-center"
            >
              <Image
                width={100}
                height={100}
                src={getImageUrl(eyewear.image)}
                alt={eyewear.name}
                className="h-12 object-contain"
              />
              <p>{eyewear.name}</p>
            </Link>
          ))}
        </div>
      </div>
    </Wrapper>
  );
};

export default EyeWearForEveryone;
