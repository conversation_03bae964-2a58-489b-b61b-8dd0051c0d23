"use client";
import React, { useEffect, useState } from "react";
import { Product } from "@/components/common/product";
import { Button } from "@/components/ui/button";
import { IPaginator, IProduct } from "@/interfaces";
import { api_interceptor } from "@/utils/api-interceptor";
import { PRODUCT_URL } from "@/constants/url";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

export const BestSellers = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [products, setProducts] = useState<IProduct[]>([]);
  const [paginator, setPaginator] = useState<IPaginator>({
    page: 0,
    limit: 4,
    total: 0,
    totalPages: 1,
  });

  const loadMore = () => {
    setLoading(true);
    api_interceptor
      .get(
        PRODUCT_URL +
          `?page=${paginator.page + 1}&limit=${paginator.limit}&sort=popularity`
      )
      .then((res) => {
        setProducts([...products, ...res.data.data.products]);
        setPaginator(res.data.data.paginator);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    loadMore();
    return () => {};
  }, []);

  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-3xl font-semibold text-center font-workSans">
          Best Sellers
        </h1>
      </div>
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {products.map((product, index) => (
          <Product key={index} product={product} />
        ))}
        {loading &&
          [1, 2, 3, 4].map((prod) => (
            <div key={prod}>
              <Skeleton className="mx-auto aspect-[2/1.8] w-full" />
              <div className="h-2" />

              <Skeleton className="h-6 w-full" />
              <div className="flex items-center space-x-2 my-3">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-20" />
              </div>
              <Skeleton className="h-6 w-20 mb-3" />
              <div className="flex space-x-2">
                {[1, 2, 3].map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "rounded-full p-[2px] cursor-pointer border border-white"
                    )}
                  >
                    <Skeleton className="h-4 w-4 rounded-full border" />
                  </div>
                ))}
              </div>
            </div>
          ))}
      </div>
      {paginator.totalPages > paginator.page && (
        <div className="flex justify-center">
          <Button onClick={loadMore}>Load more</Button>
        </div>
      )}
    </div>
  );
};
