"use client";
import Image from "next/image";
import React, { useRef, useState, useEffect } from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { fetchCategories } from "@/store/slices/category-slice";
import { getImageUrl } from "@/utils/image-url";
import { Skeleton } from "@/components/ui/skeleton";

export const ShopByCategories = () => {
  const { loading, categories } = useSelector(
    (state: RootState) => state.category
  );

  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLAnchorElement | null)[]>([]);
  const [itemWidth, setItemWidth] = useState(0);
  const [showArrows, setShowArrows] = useState({ left: false, right: true });

  // Fetch categories if not already loaded
  useEffect(() => {
    if (!categories.length) {
      store.dispatch(fetchCategories());
    }
  }, [categories.length]);

  // Calculate item width on mount and resize
  useEffect(() => {
    const calculateDimensions = () => {
      if (itemRefs.current.length > 0 && itemRefs.current[0]) {
        const firstItem = itemRefs.current[0];
        const style = window.getComputedStyle(firstItem);
        const width = firstItem.offsetWidth;
        const margin = parseFloat(style.marginRight) || 0;
        setItemWidth(width + margin);
      }
    };

    calculateDimensions();
    window.addEventListener("resize", calculateDimensions);
    return () => window.removeEventListener("resize", calculateDimensions);
  }, []);

  // Update arrow visibility on scroll
  const updateScrollVisibility = () => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      setShowArrows({
        left: scrollLeft > 0,
        right: scrollLeft < scrollWidth - clientWidth - 1,
      });
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.addEventListener("scroll", updateScrollVisibility);
    }
    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener(
          "scroll",
          updateScrollVisibility
        );
      }
    };
  }, []);

  const scroll = (direction: "left" | "right") => {
    if (containerRef.current) {
      const scrollAmount = direction === "right" ? itemWidth : -itemWidth;
      containerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <section className="relative group">
      <div className="flex items-center mb-6 space-x-2">
        {/* Left Arrow */}
        <button
          disabled={!showArrows.left}
          onClick={() => scroll("left")}
          className={cn(
            "p-2 rounded-full border-gray-200 border hover:bg-gray-100 transition-all duration-300",
            { "pointer-events-none bg-gray-200": !showArrows.left }
          )}
          aria-label="Scroll left"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        {/* Right Arrow */}
        <button
          disabled={!showArrows.right}
          onClick={() => scroll("right")}
          className={cn(
            "p-2 rounded-full border-gray-200 border hover:bg-gray-100 transition-all duration-300",
            { "pointer-events-none bg-gray-200": !showArrows.right }
          )}
          aria-label="Scroll right"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>

        <h2 className="font-semibold">SHOP BY CATEGORIES</h2>
      </div>

      {/* Categories container with hidden scrollbar */}
      <div
        ref={containerRef}
        className="flex overflow-x-auto scrollbar-none"
        onScroll={updateScrollVisibility}
        style={{
          scrollbarWidth: "none", // Firefox
          msOverflowStyle: "none", // IE/Edge
        }}
      >
        {loading &&
          [1, 2, 3, 4, 5].map((category) => (
            <Skeleton
              key={category}
              className="w-64 mr-6 last:mr-0 shadow-lg aspect-[1/1.1] border border-gray-200 rounded-lg overflow-hidden"
            />
          ))}
        {!loading &&
          categories.map((category, index) => (
            <Link
              key={`${category.slug}-${index}`}
              href={`/product?category=${category.slug}`}
              ref={(el) => {
                itemRefs.current[index] = el;
              }}
              className="flex-shrink-0 w-64 mr-6 last:mr-0 shadow-lg aspect-[1/1.1] border border-gray-200 rounded-lg overflow-hidden"
            >
              <div className="relative w-full h-full">
                <Image
                  src={getImageUrl(category.image)}
                  alt={category.name}
                  fill
                  className="object-cover transition-transform"
                  sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                />
                <div className="absolute bottom-3 left-3 px-3 py-2 rounded-sm bg-white border border-gray-200">
                  <h3 className="font-medium">{category.name}</h3>
                </div>
              </div>
            </Link>
          ))}
      </div>

      {/* Global CSS solution for hiding scrollbar */}
      <style jsx global>{`
        .scrollbar-none::-webkit-scrollbar {
          display: none;
          width: 0;
          height: 0;
          background: transparent;
        }
      `}</style>
    </section>
  );
};
