// REDUX
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

// INTERFACE
import { IUser, IUserSlice } from "@/interfaces";

// CONSTANTS
import { ADDRESS_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";
import { IAddressSlice } from "@/interfaces/address-slice";
import { IAddress } from "@/interfaces/address";
import { getErrorMessage } from "@/utils/error-message";

const initialState: IAddressSlice = {
  address: [],
  loading: true,
  error: "",
};

export const fetchAddresses = createAsyncThunk<
  IAddress[],
  void,
  FetchDataRejected
>("address/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(ADDRESS_URL)
    .then((res) => {
      return res.data.data;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Something went wrong"),
      });
    });
});

export const createAddress = createAsyncThunk<
  string,
  IAddress,
  FetchDataRejected
>("address/create", async (body, { rejectWithValue }) => {
  return api_interceptor
    .post(ADDRESS_URL, body)
    .then((res) => {
      return res.data.message;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Something went wrong"),
      });
    });
});

export const updateAddress = createAsyncThunk<
  string,
  IAddress,
  FetchDataRejected
>("address/update", async (body, { rejectWithValue }) => {
  return api_interceptor
    .put(ADDRESS_URL + "/" + body._id, body)
    .then((res) => {
      return res.data.message;
    })
    .catch((err) => {
      return rejectWithValue({
        message: getErrorMessage(err, "Something went wrong"),
      });
    });
});

const addressSlice = createSlice({
  name: "address",
  initialState,
  reducers: {
    resetAddress(state) {
      state.address = [];
      state.loading = true;
      state.error = "";
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchAddresses.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchAddresses.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.address = payload;
    });
    builder.addCase(fetchAddresses.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
    builder.addCase(createAddress.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(createAddress.fulfilled, (state, { payload }) => {
      state.loading = false;
    });
    builder.addCase(createAddress.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default addressSlice.reducer;
