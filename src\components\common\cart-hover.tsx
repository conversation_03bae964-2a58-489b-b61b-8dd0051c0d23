import { Check<PERSON>ircle2, MoveR<PERSON>, X } from "lucide-react";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import ItemSummary from "./item-summary";
import { useSelector } from "react-redux";
import { useAppDispatch } from "@/store/store";
import { fetchCart, removeFromCart } from "@/store/slices/cart-slice";
import { useRouter } from "next/navigation";
import { Coupon } from "./coupon";

export const CartHover = () => {
  const router = useRouter();
  const cart = useSelector((state: any) => state.cart);
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(fetchCart());
  }, []);

  const removeItem = (item: any, index: number) => {
    dispatch(removeFromCart(item.id));
  };

  const onCheckout = () => {
    router.push("/checkout");
  };

  return (
    <div>
      <h2 className="font-semibold uppercase text-center mb-4">
        My Basket ({cart.totalItems})
      </h2>

      {cart?.cart?.length ? (
        <>
          {cart?.cart?.map((item: any, index: number) => {
            return (
              <ItemSummary
                key={"basket-item-" + index}
                item={item}
                index={index}
                length={cart?.items?.length}
                onRemove={(item, index) => removeItem(item, index)}
              />
            );
          })}

          <Coupon />
          <div className="mt-4 text-center px-8">
            <Button
              onClick={onCheckout}
              className="uppercase w-full round-none py-6"
              size={"lg"}
            >
              <CheckCircle2 />
              Proceed To Checkout
            </Button>

            <div className="mt-4">
              <Link
                className="text-text-accent font-bold underline"
                href={"/account/cart"}
              >
                View My Basket
              </Link>
            </div>
          </div>
        </>
      ) : (
        <div className="w-80 text-center space-y-4 mt-4">
          <p className="text-center mb-2 w-full text-text-accent">
            Cart is empty
          </p>

          <Link
            href={"/product"}
            className="flex justify-center items-center text-primary"
          >
            <span className="mr-1">Browse Products</span>
            <MoveRight />
          </Link>
        </div>
      )}
    </div>
  );
};
