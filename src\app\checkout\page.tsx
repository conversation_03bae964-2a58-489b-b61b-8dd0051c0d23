"use client";
import { Wrapper } from "@/components/common/wrapper";
import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState, useAppDispatch } from "@/store/store";
import { CheckoutSteps } from "@/components/checkout/checkout-steps";
import { OrderSummary } from "@/app/account/cart/order-summary";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { setCurrentStep } from "@/store/slices/checkout-slice";
import { applyCoupon, fetchCart } from "@/store/slices/cart-slice";
import Link from "next/link";
import { toast } from "sonner";
import Image from "next/image";
import { getImageUrl } from "@/utils/image-url";

const Checkout = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const cart = useSelector((state: RootState) => state.cart);
  const { currentStep } = useSelector((state: RootState) => state.checkout);

  useEffect(() => {
    dispatch(fetchCart());
    dispatch(setCurrentStep(1));

    const intervalId = setInterval(() => {
      dispatch(fetchCart());
    }, 1000);

    // Clean up the interval when the component unmounts
    return () => clearInterval(intervalId);
  }, [dispatch]);

  const handleContinue = () => {
    if (cart.cart.length === 0) return;
    router.push("/checkout/address");
  };

  return (
    <Wrapper vertical>
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>

      <CheckoutSteps currentStep={currentStep} />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-x-8 gap-y-7">
        <div className="lg:col-span-2 space-y-6">
          <div className="border border-gray-200 rounded-md p-6">
            <h2 className="text-xl font-semibold mb-4">Your Items</h2>

            {cart.cart.length > 0 ? (
              <div className="space-y-4">
                {cart.cart.map((item, index) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between py-4 border-b border-gray-100"
                  >
                    <div className="flex items-center">
                      {item.product.thumbnail && (
                        <div className="w-16 h-16 mr-4 relative">
                          <Image
                            width={200}
                            height={200}
                            src={getImageUrl(item.product.thumbnail)}
                            alt={item.product.name}
                            className="object-contain w-full h-full"
                          />
                        </div>
                      )}
                      <div>
                        <h3 className="font-medium">{item.product.name}</h3>
                        <p className="text-sm text-gray-500">
                          Quantity: {item.quantity}
                        </p>
                      </div>
                    </div>
                    <div className="font-semibold">
                      $
                      {((item.variation?.price || item.product.basePrice) +
                        (item.lensData?.lensPackage?.price || 0)) *
                        item.quantity}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">Your cart is empty</p>
                <Link href="/">
                  <Button>Continue Shopping</Button>
                </Link>
              </div>
            )}
          </div>
        </div>

        <div className="lg:col-span-1">
          <OrderSummary
            cart={cart}
            checkoutTitle="Continue to Address"
            onCheckout={handleContinue}
            onApplyCoupon={(code) => {
              dispatch(applyCoupon(code))
                .unwrap()
                .then((response) => {
                  toast.success("Coupon applied successfully");
                })
                .catch((error) => {
                  toast.error(error.message || "Invalid coupon code");
                });
            }}
          />
        </div>
      </div>
    </Wrapper>
  );
};

export default Checkout;
