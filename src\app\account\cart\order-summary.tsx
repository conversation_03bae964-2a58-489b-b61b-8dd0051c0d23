"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { ICartSlice, ITax } from "@/interfaces";
import {
  calculateTax,
  calculateTaxes,
  getSubtotal,
  getTotal,
} from "@/utils/cart-price";
import { formatPrice } from "@/utils/format-price";
import { MoveRight, Tag, X } from "lucide-react";
import React, { useState } from "react";
import { RootState, useAppDispatch } from "@/store/store";
import { applyCoupon, removeCoupon } from "@/store/slices/cart-slice";
import { toast } from "sonner";
import { useSelector } from "react-redux";

interface OrderSummaryProps {
  cart: ICartSlice;
  isCouponVisible?: boolean;
  checkoutTitle?: string;
  onApplyCoupon?: (code: string) => void;
  onCheckout?: () => void;
  secondaryButtonTitle?: string;
  secondaryButtonOnClick?: () => void;
}

export const OrderSummary: React.FC<OrderSummaryProps> = ({
  cart,
  isCouponVisible = true,
  checkoutTitle = "Go to Checkout",
  onApplyCoupon,
  onCheckout,
  secondaryButtonOnClick,
  secondaryButtonTitle,
}) => {
  const { taxes } = useSelector((state: RootState) => state.tax);
  const [promoCode, setPromoCode] = useState("");
  const [isApplying, setIsApplying] = useState(false);
  const dispatch = useAppDispatch();

  return (
    <div className="border border-gray-200 rounded-sm px-6 py-5">
      <h2 className="text-xl font-semibold mb-6">Order Summary</h2>
      <div>
        <p className="mb-4 text-sm flex justify-between items-center text-text-accent">
          <span>Subtotal</span>
          <span className="font-bold">
            {formatPrice({ price: getSubtotal(cart.cart) })}
          </span>
        </p>

        {cart.automatic_discount?.amount ? (
          <p className="text-sm flex justify-between items-center text-text-accent">
            <span>Discount</span>
            <span className="font-bold text-red-500">
              -{formatPrice({ price: cart.automatic_discount.amount })}
            </span>
          </p>
        ) : null}

        {taxes?.length
          ? taxes.map((tax) => (
              <p
                key={tax.name}
                className="text-sm flex justify-between items-center text-text-accent"
              >
                <span>
                  {tax.name} ({tax.isInclusive ? "Inclusive" : "Exclusive"})
                </span>
                <span className="font-bold">
                  {formatPrice({ price: calculateTax(tax, cart.cart) })}
                </span>
              </p>
            ))
          : null}
      </div>

      <Separator className="py-4 my-0" />

      {cart.coupon && cart.couponDiscount > 0 ? (
        <div className="mb-6">
          <p className="text-sm flex justify-between items-center text-green-600">
            <span className="flex items-center">
              <Tag className="w-3 h-3 mr-1" />
              Coupon Discount ({cart.coupon.code})
            </span>
            <span className="font-medium">
              -{formatPrice({ price: cart.couponDiscount })}
            </span>
          </p>
          <p className="text-sm flex justify-between items-center mt-2 pt-2 border-t">
            <span className="font-semibold">Total</span>
            <span className="font-bold text-lg">
              {formatPrice({
                price: +calculateTaxes(taxes, cart.cart) + +getTotal(cart.cart),
              })}
            </span>
          </p>
        </div>
      ) : (
        <p className="text-sm flex justify-between items-center mb-6">
          <span>Total</span>
          <span className="font-bold">
            {formatPrice({
              price:
                +calculateTaxes(taxes, cart.cart) +
                getTotal(cart.cart, {
                  discount: cart.automatic_discount?.amount,
                  tax:
                    cart.taxes?.reduce((acc, cur) => acc + cur.amount, 0) || 0,
                  taxType: "flat",
                  couponDiscount: cart.couponDiscount || 0,
                }),
            })}
          </span>
        </p>
      )}

      {/* Coupon Section */}
      {cart.coupon ? (
        <div className="mb-6 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm font-medium flex items-center">
                <Tag className="w-4 h-4 mr-1 text-green-600" />
                Coupon Applied:{" "}
                <span className="font-bold ml-1">{cart.coupon.code}</span>
              </p>
              <p className="text-xs text-green-600 mt-1">
                {cart.coupon.discountType === "percentage"
                  ? `${cart.coupon.discountValue}% off your order`
                  : `${formatPrice({
                      price: cart.coupon.discountValue,
                    })} off your order`}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500 hover:text-red-500 hover:bg-red-50"
              onClick={() => {
                dispatch(removeCoupon());
                toast.success("Coupon removed");
              }}
              title="Remove coupon"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          {cart.couponDiscount > 0 && (
            <div className="mt-3 pt-2 border-t border-green-200">
              <p className="text-sm flex justify-between items-center text-green-600">
                <span>Discount amount:</span>
                <span className="font-bold">
                  -{formatPrice({ price: cart.couponDiscount })}
                </span>
              </p>
              <p className="text-sm flex justify-between items-center mt-1">
                <span className="font-medium">New total:</span>
                <span className="font-bold">
                  {formatPrice({
                    price:
                      +calculateTaxes(taxes, cart.cart) +
                      getTotal(cart.cart, {
                        discount: cart.automatic_discount?.amount,
                        couponDiscount: cart.couponDiscount || 0,
                      }),
                  })}
                </span>
              </p>
            </div>
          )}
        </div>
      ) : isCouponVisible ? (
        <div className="mb-6 p-3 border border-dashed border-gray-300 rounded-md">
          <p className="text-sm font-medium mb-2 flex items-center">
            <Tag className="w-4 h-4 mr-1" />
            Have a promo code?
          </p>
          <div className="flex items-center">
            <Input
              containerClassName="w-full mr-2"
              className="rounded-md border-gray-300 text-sm"
              placeholder="Enter code"
              value={promoCode}
              onChange={(e) => setPromoCode(e.target.value)}
              disabled={isApplying}
            />
            <Button
              className="bg-primary hover:bg-primary/90 text-sm px-3"
              onClick={() => {
                if (!promoCode.trim()) return;

                setIsApplying(true);
                if (onApplyCoupon) {
                  onApplyCoupon(promoCode);
                  setIsApplying(false);
                } else {
                  dispatch(applyCoupon(promoCode))
                    .unwrap()
                    .then((response) => {
                      toast.success("Coupon applied successfully");
                      setPromoCode("");
                    })
                    .catch((error) => {
                      toast.error(error.message || "Invalid coupon code");
                    })
                    .finally(() => {
                      setIsApplying(false);
                    });
                }
              }}
              disabled={!promoCode.trim() || isApplying}
            >
              {isApplying ? "Applying..." : "Apply"}
            </Button>
          </div>
        </div>
      ) : null}

      <Button
        className="flex items-center rounded-md w-full h-11"
        onClick={onCheckout}
        disabled={!cart?.cart?.length}
      >
        {checkoutTitle}
        <MoveRight className="ml-2" />
      </Button>
      {secondaryButtonTitle && (
        <Button
          variant={"outline"}
          className="flex items-center rounded-md w-full mt-4"
          onClick={secondaryButtonOnClick}
        >
          {secondaryButtonTitle}
        </Button>
      )}
    </div>
  );
};
