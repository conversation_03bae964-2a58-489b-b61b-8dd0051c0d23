import * as React from "react";
import { cn } from "@/lib/utils";

interface SeparatorProps {
  text?: string;
  className?: string;
  textClassName?: string;
}

export const Separator: React.FC<SeparatorProps> = ({
  text,
  className,
  textClassName,
}) => {
  return (
    <div className={cn("relative", "my-6", className)}>
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-gray-300" />
      </div>
      {text ? (
        <div className="relative flex justify-center text-sm">
          <span className={cn("bg-white px-2 text-gray-500", textClassName)}>
            {text}
          </span>
        </div>
      ) : (
        <div className="py-2" /> // keeps vertical spacing
      )}
    </div>
  );
};
