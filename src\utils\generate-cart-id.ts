import { generateUUID } from "./generate-uuid";

// TODO: UPDATE DATA AS PER PROJECT REQUIREMENT
export const getCartId = (productId: string, variationId: string) => {
  let combination = productId + variationId;
  // if (!item) return;

  // let combination = `${item.categoryId}-${item.itemId}-${item.itemName}-${item.itemPrice}`;

  // // if (item?.extraCategory) combination += `-${item?.extraCategory}`;

  // if (item?.variantId) combination += `-${item.variantId}`;
  // if (item?.attributeId) combination += `-${item.attributeId}`;

  // if (item?.attributeValues?.length) {
  //   item?.attributeValues?.filter((x: any) => {
  //     combination += `${x?.id}`;
  //   });
  // }

  // if (item?.orderItemNote && item?.orderItemNote != "")
  //   combination += `-${item.orderItemNote}`;

  return generateUUID(combination);
};
