"use client";

import React from "react";
import { LensSelectionData } from "../lens-wizard";
import { Eye, Glasses, Book, Ban } from "lucide-react";
import { LensesSection } from "../lenses-section";

interface VisionTypeStepProps {
  lensData: LensSelectionData;
  updateLensData: (data: Partial<LensSelectionData>, next: boolean) => void;
  onNext: () => void;
}

export const VisionTypeStep: React.FC<VisionTypeStepProps> = ({
  lensData,
  updateLensData,
}) => {
  const visionTypes = [
    {
      id: "distance",
      name: "Distance",
      description:
        "You mostly need glasses to see things in the distance, e.g. whilst driving.",
      icon: Eye,
      nextStep: "prescription",
    },
    {
      id: "bifocal",
      name: "Bifocal & Varifocal",
      description:
        "You need glasses to see things up close and in the distance.",
      icon: Glasses,
      nextStep: "prescription",
      items: [
        {
          id: "kodak-easy2-max",
          header: "KODAK Easy2 Max Progressive Lens",
          description:
            "The KODAK Easy2 Max Lens uses patented Vision First Design Technology to prioritise wearer clarity at all distances. This premium option is highly recommended for new and existing varifocal wearers.",
          price: 149,
        },
        {
          id: "varifocal-supreme-hd",
          header: "Varifocal Supreme HD",
          description:
            "The KODAK Easy2 Max Lens uses patented Vision First Design Technology to prioritise wearer clarity at all distances. This premium option is highly recommended for new and existing varifocal wearers.",
          price: 109,
        },
        {
          id: "varifocal-elite-hd",
          header: "Varifocal Elite HD",
          description:
            "Elite varifocal lenses have a more sophisticated design than Advanced. They feature a wider field of vision for reading, with significantly less peripheral distortion.",
          price: 79,
        },
        {
          id: "varifocal-advanced",
          header: "Varifocal Advanced",
          description:
            "Advanced lenses utilise freeform technology to offer an enhanced field of vision at all distances.",
          price: 59,
        },
        {
          id: "bifocal",
          header: "Bifocal",
          description:
            "Bifocal lenses have a visible line separating the near and far distance viewing areas.",
          price: 39,
        },
      ],
    },
    {
      id: "reading",
      name: "Reading",
      description:
        "You mostly need glasses to see things up close, e.g. papers, documents, etc.",
      icon: Book,
      nextStep: "prescription",
      items: [
        {
          id: "reading",
          header: "Reading",
          description: "Lenses for reading up close",
        },
        {
          id: "computer",
          header: "Computer",
          description: "Lenses for reading at computer screen distance.",
        },
      ],
    },
    {
      id: "non-prescription",
      name: "Non Prescription",
      description: "Basic lenses with no vision correction.",
      icon: Ban,
      nextStep: "lens-category",
    },
  ];

  const handleSelect = (
    visionType: string,
    packageId: string,
    next: boolean,
    packagePrice: number
  ) => {
    // If Non Prescription is selected, automatically set prescriptionType to "none"
    if (visionType === "non-prescription") {
      updateLensData(
        {
          visionType,
          prescriptionType: "none",
          prescription: undefined,
          packageId: "",
          packagePrice: 0,
        },
        next
      );
    } else {
      updateLensData({ visionType, packageId, packagePrice }, next);
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Vision Type</h2>
      <div className="space-y-4">
        {visionTypes.map((type) => (
          <LensesSection
            data={type}
            key={type.id}
            selected={lensData.visionType === type.id}
            packageId={lensData.packageId}
            handleSelect={handleSelect}
          />
        ))}
      </div>
    </div>
  );
};
