"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff, MoveRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import store, { RootState } from "@/store/store";
import { changePassword } from "@/store/slices/user-slice";
import { ToastErrors } from "@/components/common/toast-errors";
import { useSelector } from "react-redux";

const changePasswordSchema = z
  .object({
    currentPassword: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  })
  .refine((data) => data.password !== data.currentPassword, {
    message: "New password must be different from current password",
    path: ["password"],
  });

type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

export default function ChangePasswordPage() {
  const { error, loading, success } = useSelector(
    (state: RootState) => state.user
  );
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
  });

  const onSubmit = (data: ChangePasswordFormData) => {
    console.log("Change Password data:", data);
    // handle login here (e.g., call API)
    store.dispatch(
      changePassword({
        newPassword: data.password,
        oldPassword: data.currentPassword,
      })
    );
  };

  return (
    <div className="space-y-6">
      <ToastErrors error={error} success={success} />
      <h2 className="text-xl font-semibold mb-3">Change Password</h2>

      <p className="text-sm mb-6">
        Your new password must be different from previous used passwords.
      </p>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mb-6">
        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">
            Current Password
          </label>

          <div className="relative">
            <Input
              type={showCurrentPassword ? "text" : "password"}
              {...register("currentPassword")}
              placeholder="••••••••"
              className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            />

            <Button
              type="button"
              size={"icon"}
              className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
              onClick={() => setShowCurrentPassword((prev) => !prev)}
            >
              {showCurrentPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </Button>
          </div>

          {errors.currentPassword && (
            <p className="text-sm text-red-500 mt-1">
              {errors.currentPassword.message}
            </p>
          )}
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">Password</label>

          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              {...register("password")}
              placeholder="••••••••"
              className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            />

            <Button
              type="button"
              size={"icon"}
              className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
              onClick={() => setShowPassword((prev) => !prev)}
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </Button>
          </div>

          {errors.password && (
            <p className="text-sm text-red-500 mt-1">
              {errors.password.message}
            </p>
          )}
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">
            Confirm Password
          </label>

          <div className="relative">
            <Input
              type={showConfirm ? "text" : "password"}
              {...register("confirmPassword")}
              placeholder="••••••••"
              className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            />

            <Button
              type="button"
              size={"icon"}
              className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
              onClick={() => setShowConfirm((prev) => !prev)}
            >
              {showConfirm ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </Button>
          </div>

          {errors.confirmPassword && (
            <p className="text-sm text-red-500 mt-1">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        <Button
          disabled={isSubmitting}
          size={"lg"}
          className="w-full py-4 font-semibold uppercase mb-6"
        >
          {isSubmitting || loading ? (
            "Submitting..."
          ) : (
            <>
              Submit <MoveRight />{" "}
            </>
          )}
        </Button>
      </form>
    </div>
  );
}
