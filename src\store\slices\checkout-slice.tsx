// REDUX
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// INTERFACES
import { CheckoutState, PaymentDetails } from "@/interfaces/checkout";
import { IAddress } from "@/interfaces/address";

const initialState: CheckoutState = {
  currentStep: 1,
  shippingAddress: null,
  billingAddress: null,
  sameAsShipping: true,
  paymentDetails: null,
  orderNotes: "",
};

const checkoutSlice = createSlice({
  name: "checkout",
  initialState,
  reducers: {
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },
    setShippingAddress: (state, action: PayloadAction<IAddress>) => {
      state.shippingAddress = action.payload;
      if (state.sameAsShipping) {
        state.billingAddress = action.payload;
      }
    },
    setBillingAddress: (state, action: PayloadAction<IAddress>) => {
      state.billingAddress = action.payload;
    },
    setSameAsShipping: (state, action: PayloadAction<boolean>) => {
      state.sameAsShipping = action.payload;
      if (action.payload && state.shippingAddress) {
        state.billingAddress = state.shippingAddress;
      }
    },
    setPaymentDetails: (state, action: PayloadAction<PaymentDetails>) => {
      state.paymentDetails = action.payload;
    },
    setOrderNotes: (state, action: PayloadAction<string>) => {
      state.orderNotes = action.payload;
    },
    resetCheckout: () => initialState,
  },
});

export const {
  setCurrentStep,
  setShippingAddress,
  setBillingAddress,
  setSameAsShipping,
  setPaymentDetails,
  setOrderNotes,
  resetCheckout,
} = checkoutSlice.actions;

export default checkoutSlice.reducer;
