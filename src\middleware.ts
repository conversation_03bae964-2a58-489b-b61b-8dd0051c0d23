import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

export async function middleware(request: NextRequest) {
  const token = await getToken({ req: request });

  const isAuthRoute = request.nextUrl.pathname.startsWith("/auth");

  const loggedIn =
    request.cookies.get("next-auth.session-token") ||
    request.cookies.get("__Secure-next-auth.session-token");

  if (isAuthRoute && loggedIn) {
    // If the user is already logged in, prevent access to auth routes
    return NextResponse.redirect(new URL("/", request.url));
  }

  if (!isAuthRoute && !loggedIn) {
    // If the user is not logged in, redirect them to the auth page
    return NextResponse.redirect(new URL("/auth", request.url));
  }

  return NextResponse.next(); // Allow the request to continue
}

export const config = {
  matcher: ["/account/:path*", "/auth", "/auth/:path*"],
};
