"use client";

import { Product } from "@/components/common/product";
import { Pagination } from "@/components/ui/pagination";
import { RootState } from "@/store/store";
import React, { useState } from "react";
import { useSelector } from "react-redux";

const Page = () => {
  const { wishlist } = useSelector((state: RootState) => state.wishlist);
  // const [currentPage, setCurrentPage] = useState(1);

  return (
    <div className="space-y-3">
      <h2 className="text-xl font-semibold">My Wishlist</h2>

      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {wishlist.map(({ product }, index) => (
          <Product key={"wishlist-" + index} product={product} />
        ))}
      </div>

      {/* <div className="flex justify-center">
        <Pagination
          currentPage={currentPage}
          totalPages={6}
          buttonClassName="rounded-full"
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div> */}
    </div>
  );
};

export default Page;
