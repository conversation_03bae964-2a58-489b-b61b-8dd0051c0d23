"use client";
import React, { useState, useEffect, useMemo, Suspense } from "react";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
} from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import {
  ChevronDownIcon,
  FunnelIcon,
  ChevronUpIcon,
} from "@heroicons/react/20/solid";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Product } from "@/components/common/product";
import Image from "next/image";
import { IFilter, IBreadcrumb } from "@/interfaces";
import { TopBreadcrumb } from "@/components/common/top-breadcrumb";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { fetchCategories } from "@/store/slices/category-slice";
import { fetchProducts } from "@/store/slices/product-slice";
import { fetchAttributes } from "@/store/slices/attribute-slice";
import { fetchFrameShapes } from "@/store/slices/frame-shape-slice";
import { fetchEyeWears } from "@/store/slices/eye-wear-slice";
import { getImageUrl } from "@/utils/image-url";
import { useRouter, useSearchParams } from "next/navigation";

const sortOptions = [
  { name: "Most Popular", sort: "popularity" },
  { name: "Best Rating", sort: "rating" },
  { name: "Newest", sort: "newest" },
  {
    name: "Price: Low to High",
    sort: "low-to-high",
  },
  {
    name: "Price: High to Low",
    sort: "high-to-low",
  },
];

const Page = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const {
    attribute: { attributes },
    category: { categories },
    product: { products, paginator },
    frameShape: { frameShapes },
    eyeWear: { eyeWears },
  } = useSelector((state: RootState) => state);
  const urlCategory = searchParams.get("category");
  const urlAttribute = searchParams.get("attribute");
  const urlFrameShape = searchParams.get("frameShape");
  const urlEyeWear = searchParams.get("eyeWear");
  const urlSort = searchParams.get("sort");

  const [sortOption, setSortOption] = useState(urlSort || "Newest");

  const [filters, setFilters] = useState<IFilter[]>([]);
  const [appliedFilters, setAppliedFilters] = useState<
    Record<string, string[]>
  >({
    category: urlCategory?.split(",") ?? [],
    attribute: urlAttribute?.split(",") ?? [],
    frameShape: urlFrameShape?.split(",") ?? [],
    eyeWear: urlEyeWear?.split(",") ?? [],
  });

  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const breadCrumbs: IBreadcrumb[] = [
    { label: "Home", href: "/" },
    { label: "Products", href: "" },
  ];

  useEffect(() => {
    if (!categories.length) {
      store.dispatch(fetchCategories());
    }

    if (!attributes.length) {
      store.dispatch(fetchAttributes());
    }

    if (!frameShapes.length) {
      store.dispatch(fetchFrameShapes());
    }

    if (!eyeWears.length) {
      store.dispatch(fetchEyeWears());
    }

    return () => {};
  }, []);

  useEffect(() => {
    store.dispatch(
      fetchProducts({
        page: 1,
        limit: 10,
        sort: sortOption,
        attribute: appliedFilters.attribute,
        category: appliedFilters.category,
        frameShape: appliedFilters.frameShape,
        eyeWear: appliedFilters.eyeWear,
      })
    );
    return () => {};
  }, [sortOption, appliedFilters]);

  useEffect(() => {
    const filtersData = [
      {
        id: "category",
        name: "Category",
        options: categories.map((category) => ({
          value: category.slug,
          label: category.name,
          checked: false,
        })),
      },
      {
        id: "gender-age",
        name: "Gender & Age",
        options: [
          { value: "women", label: "Women", checked: true },
          { value: "men", label: "Men", checked: false },
          { value: "kids", label: "Kids", checked: false },
        ],
      },
      {
        id: "frameShape",
        name: "Frame shape",
        options: frameShapes.map((frameShape) => ({
          value: frameShape.slug,
          label: frameShape.name,
          checked: false,
          image: frameShape.image,
        })),
      },
      {
        id: "eyeWear",
        name: "Eye wear",
        options: eyeWears.map((eyeWear) => ({
          value: eyeWear.slug,
          label: eyeWear.name,
          checked: false,
          image: eyeWear.image,
        })),
      },

      ...attributes.map((attribute) => ({
        id: "attribute",
        name: "Frame " + attribute.name,
        options: attribute.values.map((value) => ({
          value: attribute.slug + "-" + value.slug,
          label: value.value,
          checked: false,
        })),
      })),
    ];
    setFilters(filtersData);
    return () => {};
  }, [attributes.length, eyeWears.length, frameShapes.length]);

  const onApplyFilter = (
    filterId: string,
    filterValue: string,
    checked: boolean
  ) => {
    const newFilters: Record<string, string[]> = { ...appliedFilters };

    if (!newFilters[filterId]) {
      newFilters[filterId] = [];
    }

    if (checked) {
      newFilters[filterId].push(filterValue);
    } else {
      newFilters[filterId] = newFilters[filterId].filter(
        (value) => value !== filterValue
      );
    }
    setAppliedFilters(newFilters);

    const params = new URLSearchParams(searchParams.toString());
    params.delete(filterId);
    params.append(filterId, newFilters[filterId].join(","));
    router.replace("/product?" + params.toString(), { scroll: false });
  };

  const onChangeSort = (sort: string) => {
    setSortOption(sort);
    const params = new URLSearchParams(searchParams.toString());
    params.delete("sort");
    params.append("sort", sort);
    router.replace("/product?" + params.toString(), { scroll: false });
  };

  return (
    <div className="bg-white">
      <div>
        {/* Mobile filter dialog */}
        <Dialog
          open={mobileFiltersOpen}
          onClose={setMobileFiltersOpen}
          className="relative z-40 lg:hidden"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-black/25 transition-opacity duration-300 ease-linear data-closed:opacity-0"
          />

          <div className="fixed inset-0 z-40 flex">
            <DialogPanel
              transition
              className="relative ml-auto flex size-full max-w-xs transform flex-col overflow-y-auto bg-white py-4 pb-12 shadow-xl transition duration-300 ease-in-out data-closed:translate-x-full"
            >
              <div className="flex items-center justify-between px-4">
                <h2 className="text-lg font-medium text-gray-900">Filters</h2>
                <button
                  type="button"
                  onClick={() => setMobileFiltersOpen(false)}
                  className="-mr-2 flex size-10 items-center justify-center rounded-md bg-white p-2 text-gray-400"
                >
                  <span className="sr-only">Close menu</span>
                  <XMarkIcon aria-hidden="true" className="size-6" />
                </button>
              </div>

              {/* Filters */}
              <form className="mt-4 border-t border-gray-200">
                <h3 className="sr-only">Categories</h3>

                {filters.map((section, index) => (
                  <Disclosure
                    key={section.id + index}
                    as="div"
                    className="border-t border-gray-200 px-4 py-6"
                  >
                    {({ open }) => (
                      <>
                        {" "}
                        <h3 className="-mx-2 -my-3 flow-root">
                          <DisclosureButton className="group flex w-full items-center justify-between bg-white px-2 py-3 text-gray-400 hover:text-gray-500">
                            <span className="font-medium text-gray-900 first-letter:uppercase">
                              {section.name.toLowerCase()}
                            </span>
                            <span className="ml-6 flex items-center">
                              <ChevronDownIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "hidden" : "block"
                                }`}
                              />
                              <ChevronUpIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "block" : "hidden"
                                }`}
                              />
                            </span>
                          </DisclosureButton>
                        </h3>
                        <DisclosurePanel className="pt-6">
                          <div className="space-y-6">
                            {section.options.map((option, optionIdx) => (
                              <div
                                key={option.value + optionIdx}
                                className="flex items-center gap-3"
                              >
                                <div className="flex h-5 shrink-0 items-center">
                                  <div className="group grid size-4 grid-cols-1">
                                    <Checkbox
                                      defaultValue={option.value}
                                      checked={appliedFilters[
                                        section.id
                                      ]?.includes(option.value)}
                                      onCheckedChange={(e) =>
                                        onApplyFilter(
                                          section.id,
                                          option.value,
                                          Boolean(e)
                                        )
                                      }
                                      id={`filter-${section.id}-${optionIdx}`}
                                      name={`${section.id}[]`}
                                    />
                                  </div>
                                </div>
                                {option?.image && (
                                  <Image
                                    src={getImageUrl(option.image)}
                                    alt="Filter image"
                                    width={200}
                                    height={200}
                                    className="h-10 w-10 object-contain"
                                  />
                                )}
                                <label
                                  htmlFor={`filter-${section.id}-${optionIdx}`}
                                  className="min-w-0 flex-1 text-gray-500 first-letter:uppercase"
                                >
                                  {option.label.toLowerCase()}
                                </label>
                              </div>
                            ))}
                          </div>
                        </DisclosurePanel>
                      </>
                    )}
                  </Disclosure>
                ))}
              </form>
            </DialogPanel>
          </div>
        </Dialog>

        <div className="mx-auto px-4 sm:px-6 lg:px-8">
          <section aria-labelledby="products-heading" className="pt-6 pb-24">
            <TopBreadcrumb breadcrumbs={breadCrumbs} />

            <div className="grid grid-cols-1 gap-x-8 gap-y-10 lg:grid-cols-4">
              {/* Filters */}
              <form className="hidden lg:block">
                {filters.map((section, index) => (
                  <Disclosure
                    key={section.id + index}
                    as="div"
                    className="border-b border-gray-200 py-6"
                  >
                    {({ open }) => (
                      <>
                        {" "}
                        <h3 className="-my-3 flow-root">
                          <DisclosureButton className="group flex w-full items-center justify-between bg-white py-3 text-sm text-gray-400 hover:text-gray-500">
                            <span className="font-medium text-gray-900 first-letter:uppercase">
                              {section.name.toLowerCase()}
                            </span>
                            <span className="ml-6 flex items-center">
                              <ChevronDownIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "hidden" : "block"
                                }`}
                              />
                              <ChevronUpIcon
                                aria-hidden="true"
                                className={`size-5 ${
                                  open ? "block" : "hidden"
                                }`}
                              />
                            </span>
                          </DisclosureButton>
                        </h3>
                        <DisclosurePanel className="pt-6">
                          <div className="space-y-4">
                            {section.options.map((option, optionIdx) => (
                              <div
                                key={option.value + optionIdx}
                                className="flex items-center gap-3"
                              >
                                <div className="flex h-5 shrink-0 items-center">
                                  <div className="group grid size-4 grid-cols-1">
                                    <Checkbox
                                      defaultValue={option.value}
                                      id={`filter-${section.id}-${optionIdx}`}
                                      name={`${section.id}[]`}
                                      checked={appliedFilters[
                                        section.id
                                      ]?.includes(option.value)}
                                      onCheckedChange={(e) =>
                                        onApplyFilter(
                                          section.id,
                                          option.value,
                                          Boolean(e)
                                        )
                                      }
                                    />
                                  </div>
                                </div>

                                {option?.image && (
                                  <Image
                                    src={getImageUrl(option.image)}
                                    alt="Filter image"
                                    width={200}
                                    height={200}
                                    className="h-10 w-10"
                                  />
                                )}
                                <label
                                  htmlFor={`filter-${section.id}-${optionIdx}`}
                                  className="min-w-0 flex-1 text-gray-500 first-letter:uppercase"
                                >
                                  {option.label.toLowerCase()}
                                </label>
                              </div>
                            ))}
                          </div>
                        </DisclosurePanel>
                      </>
                    )}
                  </Disclosure>
                ))}
              </form>

              {/* Product grid */}
              <div className="lg:col-span-3">
                <div>
                  <Image
                    src={"/category/banner/1.svg"}
                    alt="Banner"
                    width={1000}
                    height={1000}
                    className="w-full rounded-lg"
                  />
                </div>
                <div className="flex items-center justify-between border-b border-gray-200 py-6 mb-6">
                  <h1 className="text-2xl font-semibold tracking-tight text-gray-900">
                    Products
                  </h1>

                  <div className="flex items-center gap-4">
                    <p>
                      Showing 1-
                      {Math.min(
                        paginator.page * paginator.limit,
                        paginator.total
                      )}{" "}
                      of {paginator.total} results
                    </p>
                    <Menu as="div" className="relative inline-block text-left">
                      <div>
                        <MenuButton className="group inline-flex justify-center text-sm font-medium text-gray-700 hover:text-gray-900">
                          (
                          <span className="first-letter:uppercase">
                            {
                              sortOptions.find((opt) => opt.sort === sortOption)
                                ?.name
                            }
                          </span>
                          ){""} Sort
                          <ChevronDownIcon
                            aria-hidden="true"
                            className="-mr-1 ml-1 size-5 shrink-0 text-gray-400 group-hover:text-gray-500"
                          />
                        </MenuButton>
                      </div>

                      <MenuItems
                        transition
                        className="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white ring-1 shadow-2xl ring-black/5 transition focus:outline-hidden data-closed:scale-95 data-closed:transform data-closed:opacity-0 data-enter:duration-100 data-enter:ease-out data-leave:duration-75 data-leave:ease-in"
                      >
                        <div className="py-1">
                          {sortOptions.map((option) => (
                            <MenuItem key={option.name}>
                              <p
                                onClick={() => onChangeSort(option.sort)}
                                className={cn(
                                  sortOption === option.sort
                                    ? "font-medium text-gray-900"
                                    : "text-gray-500",
                                  "block px-4 py-2 text-sm data-focus:bg-gray-100 data-focus:outline-hidden cursor-pointer"
                                )}
                              >
                                {option.name}
                              </p>
                            </MenuItem>
                          ))}
                        </div>
                      </MenuItems>
                    </Menu>
                    <button
                      type="button"
                      onClick={() => setMobileFiltersOpen(true)}
                      className="-m-2 p-2 text-gray-400 hover:text-gray-500 sm:ml-6 lg:hidden"
                    >
                      <span className="sr-only">Filters</span>
                      <FunnelIcon aria-hidden="true" className="size-5" />
                    </button>
                  </div>
                </div>

                <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3">
                  {products.map((product, index) => (
                    <Product key={index} product={product} />
                  ))}
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default function ProductPage() {
  return (
    // You could have a loading skeleton as the `fallback` too
    <Suspense>
      <Page />
    </Suspense>
  );
}
