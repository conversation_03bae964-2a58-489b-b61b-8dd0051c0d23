import React from "react";
import { Button } from "../ui/button";
import ItemSummary from "./item-summary";
import Link from "next/link";
import { useSelector } from "react-redux";
import store, { RootState, useAppDispatch } from "@/store/store";
import { IWishlist } from "@/interfaces";
import {
  fetchWishlist,
  removeFromWishlist,
} from "@/store/slices/wishlist-slice";

export const WishlistHover = () => {
  const { wishlist } = useSelector((state: RootState) => state.wishlist);
  const dispatch = useAppDispatch();

  const removeItem = (item: IWishlist, index: number) => {
    store
      .dispatch(removeFromWishlist({ productId: item.product._id }))
      .then((res) => {
        if (res.meta.requestStatus === "fulfilled") {
          dispatch(fetchWishlist({}));
        }
      });
  };

  return (
    <div>
      <h2 className="font-semibold uppercase text-center mb-4">
        My Favorites ({wishlist.length})
      </h2>

      {wishlist.length ? (
        <>
          {wishlist?.map((item: any, index: number) => {
            return (
              <ItemSummary
                key={"basket-item-" + index}
                item={item}
                index={index}
                length={wishlist?.length}
                onRemove={(item, index) => removeItem(item, index)}
              />
            );
          })}

          <div className="mt-4 text-center px-8">
            <Button className="uppercase w-full round-none p-0" size={"lg"}>
              <Link className="w-full py-6" href={"/account/wishlist"}>
                View Wishlist
              </Link>
            </Button>
          </div>
        </>
      ) : (
        <div className="w-80 text-center space-y-4 mt-4">
          <p className="text-center mb-2 w-full text-text-accent">
            No items added to wishlist
          </p>
        </div>
      )}
    </div>
  );
};
