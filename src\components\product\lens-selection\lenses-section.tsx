import React, { useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { formatPrice } from "@/utils/format-price";

interface LensesSectionProps {
  data: {
    id: string;
    name: string;
    description: string;
    icon: LucideIcon;
    nextStep: string;
    items?: {
      id: string;
      header: string;
      description: string;
      price?: number;
    }[];
  };
  selected: boolean;
  packageId: string;
  handleSelect: (
    id: string,
    packageId: string,
    next: boolean,
    packagePrice: number
  ) => void;
}

export const LensesSection: React.FC<LensesSectionProps> = ({
  data,
  selected,
  handleSelect,
  packageId,
}) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  const handleAccordionSelect = () => {
    if (data.items?.length) {
      setExpanded(!expanded);
    }
    handleSelect(data.id, "", !data?.items?.length, 0);
  };

  const handleAccordionItemSelect = (packageId: string, price: number) => {
    handleSelect(data.id, packageId, true, price);
  };

  return (
    <div>
      <div
        key={data.id}
        className={cn(
          "p-4 border rounded-md cursor-pointer border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-all",
          selected && "border-primary bg-primary/5"
        )}
        onClick={handleAccordionSelect}
      >
        <div className="flex items-start">
          <div className="mr-4">
            <data.icon
              className={`w-6 h-6 ${
                selected ? "text-primary" : "text-gray-500"
              }`}
            />
          </div>
          <div>
            <h3 className="font-medium">{data.name}</h3>
            <p className="text-sm text-gray-500 mt-1">{data.description}</p>
          </div>
        </div>
      </div>
      {data.items?.length && (
        <div
          className={cn(
            "my-3 max-h-0 overflow-hidden transition-all duration-300",
            expanded && "max-h-screen"
          )}
        >
          {data.items.map((item, index) => (
            <div
              onClick={() =>
                handleAccordionItemSelect(item.id, item.price || 0)
              }
              className="p-4 border-b cursor-pointer border-gray-200 space-y-2"
              key={index}
            >
              <div className="flex justify-between">
                <h3
                  className={cn(
                    "font-medium bg-gray-100 transition-all duration-300 hover:bg-gray-200 px-3 py-2 rounded-md",
                    packageId == item.id &&
                      "border border-primary bg-primary/10 text-primary hover:bg-primary/30"
                  )}
                >
                  {item.header}
                </h3>
                {item.price && <p>{formatPrice({ price: item.price || 0 })}</p>}
              </div>
              <p className="text-gray-500">{item.description}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
