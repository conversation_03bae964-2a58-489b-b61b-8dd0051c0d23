"use client";
import React, { useEffect } from "react";

// NEXT AUTH
import { useSession, signOut } from "next-auth/react";

// INTERCEPTOR
import { api_interceptor } from "@/utils/api-interceptor";

// THIRD PARTY LIBRARY
import { AxiosError, AxiosResponse } from "axios";
import store from "./store";
import { fetchCart } from "./slices/cart-slice";
import { fetchProfile } from "./slices/user-slice";
import { fetchWishlist } from "./slices/wishlist-slice";
import { fetchTaxes } from "./slices/tax-slice";

export function ProviderChildren({ children }: { children: React.ReactNode }) {
  const { data } = useSession();

  useEffect(() => {
    const user: any = data?.user;
    store.dispatch(fetchCart());
    store.dispatch(fetchTaxes());
    api_interceptor.interceptors.response.use(
      (res: AxiosResponse) => res,
      async (e: AxiosError) => {
        if (e?.response?.status === 401) {
          // await signOut();
          console.log(e);
        }
        return Promise.reject(e);
      }
    );

    if (user) {
      const token = `Bearer ${user?.token}`;
      api_interceptor.defaults.headers.common["Authorization"] = token;
      store.dispatch(fetchProfile({ token }));
      store.dispatch(fetchWishlist({ token }));
    }

    return () => {};
  }, [data]);

  return <div>{children}</div>;
}
