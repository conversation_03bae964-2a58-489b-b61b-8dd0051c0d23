// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IOrder, IOrderSlice, IPaginator } from "@/interfaces";
import { FetchDataRejected } from "@/interfaces/reject-type";

// CONSTANTS
import { ORDER_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { getErrorMessage } from "@/utils/error-message";

const initialState: IOrderSlice = {
  orders: [],
  success: "",
  paginator: {
    limit: 10,
    page: 1,
    total: 0,
    totalPages: 1,
  },
  currentOrder: null,
  loading: false,
  error: "",
};

// Fetch all orders
export const fetchOrders = createAsyncThunk<
  { orders: IOrder[]; paginator: IPaginator },
  { page?: number; limit?: number; filter?: string; days: number },
  FetchDataRejected
>(
  "orders/fetchAll",
  async ({ page = 1, limit = 10, filter = "", days }, { rejectWithValue }) => {
    const query = new URLSearchParams();
    query.append("page", String(page));
    query.append("limit", String(limit));
    query.append("days", String(days));

    if (filter) {
      query.append("filter", filter);
    }

    const url = `${ORDER_URL}?${query}`;

    try {
      const response = await api_interceptor.get(url);
      return response.data.data;
    } catch (err) {
      return rejectWithValue({
        message: getErrorMessage(err, "Failed to fetch orders"),
      });
    }
  }
);

// Fetch a single order by ID
export const fetchOrderById = createAsyncThunk<
  IOrder,
  string,
  FetchDataRejected
>("orders/fetchById", async (orderId, { rejectWithValue }) => {
  const url = `${ORDER_URL}/${orderId}`;

  try {
    const response = await api_interceptor.get(url);
    return response.data.data;
  } catch (err) {
    return rejectWithValue({
      message: getErrorMessage(err, "Failed to fetch order details"),
    });
  }
});

// Create a new order
export const createOrder = createAsyncThunk<
  { data: IOrder; message: string },
  any,
  FetchDataRejected
>("orders/create", async (orderData, { rejectWithValue }) => {
  try {
    const response = await api_interceptor.post(ORDER_URL, orderData);
    return response.data;
  } catch (err) {
    return rejectWithValue({
      message: getErrorMessage(err, "Failed to create order"),
    });
  }
});

const orderSlice = createSlice({
  name: "order",
  initialState,
  reducers: {
    clearOrderState: (state) => {
      state.orders = [];
      state.currentOrder = null;
      state.loading = false;
      state.error = "";
      state.success = "";
    },
  },
  extraReducers: (builder) => {
    // Fetch all orders
    builder.addCase(fetchOrders.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchOrders.fulfilled, (state, action) => {
      state.orders = action.payload.orders;
      state.paginator = action.payload.paginator;
      state.loading = false;
    });
    builder.addCase(fetchOrders.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload?.message || "Failed to fetch orders";
    });

    // Fetch order by ID
    builder.addCase(fetchOrderById.pending, (state) => {
      state.loading = true;
      state.error = "";
      state.currentOrder = null;
    });
    builder.addCase(fetchOrderById.fulfilled, (state, action) => {
      state.currentOrder = action.payload;
      state.loading = false;
    });
    builder.addCase(fetchOrderById.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload?.message || "Failed to fetch order details";
      state.currentOrder = null;
    });

    // Create order
    builder.addCase(createOrder.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(createOrder.fulfilled, (state, action) => {
      state.currentOrder = action.payload.data;
      state.loading = false;
      state.success = action.payload.message;
    });
    builder.addCase(createOrder.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload?.message || "Failed to create order";
    });
  },
});

export const { clearOrderState } = orderSlice.actions;
export default orderSlice.reducer;
