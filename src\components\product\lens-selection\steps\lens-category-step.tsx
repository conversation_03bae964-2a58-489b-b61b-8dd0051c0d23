"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { LensSelectionData } from "../lens-wizard";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import Image from "next/image";
import { getImageUrl } from "@/utils/image-url";

interface LensCategoryStepProps {
  lensData: LensSelectionData;
  updateLensData: (data: Partial<LensSelectionData>) => void;
  onNext: () => void;
  onBack: () => void;
}

export const LensCategoryStep: React.FC<LensCategoryStepProps> = ({
  lensData,
  updateLensData,
  onNext,
  onBack,
}) => {
  const { lensCategories } = useSelector(
    (state: RootState) => state.lensCategory
  );

  const handleSelect = (category: string) => {
    updateLensData({ lensCategory: category });
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Lens Category</h2>
      <div className="space-y-4">
        {lensCategories.map((category) => (
          <div
            key={category._id}
            className={`p-4 border rounded-md cursor-pointer transition-all ${
              lensData.lensCategory === category.name
                ? "border-primary bg-primary/5"
                : "border-gray-200 hover:border-primary/50"
            }`}
            onClick={() => handleSelect(category.name)}
          >
            <div className="flex items-start">
              <div className="mr-4">
                <Image
                  src={getImageUrl(category.image)}
                  width={200}
                  height={200}
                  alt="category image"
                  className="w-[50px] h-[50px] object-contain"
                />
              </div>
              <div>
                <h3 className="font-medium">{category.name}</h3>
                <p className="text-sm text-gray-500 mt-1">
                  {category.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className="flex justify-between mt-6">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onNext} disabled={!lensData.lensCategory}>
          Next
        </Button>
      </div>
    </div>
  );
};
