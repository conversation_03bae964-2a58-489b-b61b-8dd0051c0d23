// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IBanner, IBannerSlice } from "@/interfaces";

// CONSTANTS
import { BANNER_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: IBannerSlice = {
  banners: [],
  loading: true,
  error: "",
};

export const fetchBanners = createAsyncThunk<
  IBanner[],
  void,
  FetchDataRejected
>("banner/fetch", async (_, { rejectWithValue }) => {
  return api_interceptor
    .get(BANNER_URL + "?page=1&limit=100")
    .then((res) => {
      return res.data.data.banners;
    })
    .catch((err) => {
      return rejectWithValue({ message: err.message || err });
    });
});

const bannersSlice = createSlice({
  name: "banner",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchBanners.pending, (state) => {
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchBanners.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.banners = payload;
    });
    builder.addCase(fetchBanners.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default bannersSlice.reducer;
