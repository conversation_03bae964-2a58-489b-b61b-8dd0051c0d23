"use client";

import { Wrapper } from "@/components/common/wrapper";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { CheckoutSteps } from "@/components/checkout/checkout-steps";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import {
  setBillingAddress,
  setSameAsShipping,
  setShippingAddress,
  setCurrentStep,
} from "@/store/slices/checkout-slice";
import { fetchAddresses } from "@/store/slices/address-slice";
import { Checkbox } from "@/components/ui/checkbox";

import { Plus, Tag } from "lucide-react";
import { IAddress } from "@/interfaces/address";
import { formatPrice } from "@/utils/format-price";
import { AddressPopup } from "@/components/checkout/address-popup";
import { toast } from "sonner";

const AddressPage = () => {
  const router = useRouter();
  const { address } = useSelector((state: RootState) => state.address);
  const { shippingAddress, billingAddress, sameAsShipping } = useSelector(
    (state: RootState) => state.checkout
  );
  const { user } = useSelector((state: RootState) => state.user);
  const cart = useSelector((state: RootState) => state.cart);

  const [selectedShippingId, setSelectedShippingId] = useState<string | null>(
    shippingAddress?._id || null
  );
  const [selectedBillingId, setSelectedBillingId] = useState<string | null>(
    billingAddress?._id || null
  );
  const [isSameAddress, setIsSameAddress] = useState(sameAsShipping);
  const [isAddressPopupOpen, setIsAddressPopupOpen] = useState(false);

  useEffect(() => {
    store.dispatch(setCurrentStep(2));
    if (user) {
      store.dispatch(fetchAddresses());
    }
  }, [user]);

  const handleShippingAddressSelect = (address: IAddress) => {
    setSelectedShippingId(address._id);
    store.dispatch(setShippingAddress(address));
    if (isSameAddress) {
      setSelectedBillingId(address._id);
      store.dispatch(setBillingAddress(address));
    }
  };

  const handleBillingAddressSelect = (address: IAddress) => {
    setSelectedBillingId(address._id);
    store.dispatch(setBillingAddress(address));
  };

  const handleSameAddressChange = (checked: boolean) => {
    setIsSameAddress(checked);
    store.dispatch(setSameAsShipping(checked));
    if (checked && selectedShippingId) {
      const shippingAddr = address.find((a) => a._id === selectedShippingId);
      if (shippingAddr) {
        setSelectedBillingId(shippingAddr._id);
        store.dispatch(setBillingAddress(shippingAddr));
      }
    }
  };

  const handleContinue = () => {
    if (!selectedShippingId || (!isSameAddress && !selectedBillingId)) {
      // Show error or validation message
      toast("Please select shipping and billing addresses");
      return;
    }
    router.push("/checkout/payment");
  };

  const renderAddressCard = (
    addressItem: IAddress,
    isSelected: boolean,
    onSelect: () => void
  ) => (
    <div
      key={addressItem._id}
      className={`border rounded-sm ${
        isSelected ? "border-primary" : "border-gray-200"
      } p-4 cursor-pointer`}
      onClick={onSelect}
    >
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-semibold">{addressItem.fullName}</h3>
        {addressItem.isDefault && (
          <span className="text-xs bg-gray-100 px-2 py-1 rounded">Default</span>
        )}
      </div>
      <div className="space-y-1 text-sm text-gray-600">
        <p>{addressItem.address}</p>
        <p>
          {addressItem.city}, {addressItem.state} {addressItem.postalCode}
        </p>
        <p>{addressItem.country}</p>
        <p className="mt-2">Phone: {addressItem.phone}</p>
      </div>
    </div>
  );

  useEffect(() => {
    if ((!shippingAddress || !billingAddress) && address.length > 0) {
      const defaultAddress = address.find((a) => a.isDefault);
      handleShippingAddressSelect(defaultAddress || address[0]);
      handleBillingAddressSelect(defaultAddress || address[0]);
    }
    return () => {};
  }, [address.length]);

  return (
    <Wrapper vertical>
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>

      <AddressPopup
        isOpen={isAddressPopupOpen}
        onClose={() => setIsAddressPopupOpen(false)}
      />

      <CheckoutSteps currentStep={2} />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-x-8 gap-y-7">
        <div className="lg:col-span-2 space-y-8">
          {/* Shipping Address Section */}
          <div className="border border-gray-200 rounded-md p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Shipping Address</h2>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => setIsAddressPopupOpen(true)}
              >
                <Plus className="w-4 h-4 mr-1" /> Add New Address
              </Button>
            </div>

            {address.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {address.map((addr) =>
                  renderAddressCard(addr, selectedShippingId === addr._id, () =>
                    handleShippingAddressSelect(addr)
                  )
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No addresses found</p>
                <Button onClick={() => setIsAddressPopupOpen(true)}>
                  Add New Address
                </Button>
              </div>
            )}
          </div>

          {/* Billing Address Section */}
          <div className="border border-gray-200 rounded-md p-6">
            <div className="flex items-center mb-6">
              <h2 className="text-xl font-semibold">Billing Address</h2>
            </div>

            <div className="mb-6 flex items-center space-x-2">
              <Checkbox
                id="same-address"
                checked={isSameAddress}
                onCheckedChange={handleSameAddressChange}
              />
              <label
                htmlFor="same-address"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Same as shipping address
              </label>
            </div>

            {!isSameAddress && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {address.map((addr) =>
                  renderAddressCard(addr, selectedBillingId === addr._id, () =>
                    handleBillingAddressSelect(addr)
                  )
                )}
              </div>
            )}
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="border border-gray-200 rounded-md p-6 sticky top-24">
            <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

            {/* Selected Addresses Summary */}
            <div className="space-y-4 mb-6">
              <div>
                <h3 className="font-medium text-sm text-gray-500">
                  Shipping Address:
                </h3>
                {selectedShippingId ? (
                  <div className="text-sm mt-1">
                    {
                      address.find((a) => a._id === selectedShippingId)
                        ?.fullName
                    }
                  </div>
                ) : (
                  <div className="text-sm text-red-500 mt-1">
                    Please select a shipping address
                  </div>
                )}
              </div>

              <div>
                <h3 className="font-medium text-sm text-gray-500">
                  Billing Address:
                </h3>
                {isSameAddress ? (
                  <div className="text-sm mt-1">Same as shipping address</div>
                ) : selectedBillingId ? (
                  <div className="text-sm mt-1">
                    {address.find((a) => a._id === selectedBillingId)?.fullName}
                  </div>
                ) : (
                  <div className="text-sm text-red-500 mt-1">
                    Please select a billing address
                  </div>
                )}
              </div>

              {/* Coupon Information */}
              {cart.coupon && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center">
                    <Tag className="w-4 h-4 mr-2 text-green-600" />
                    <h3 className="font-medium text-sm">Applied Coupon:</h3>
                  </div>
                  <div className="mt-1 pl-6">
                    <p className="text-sm font-medium">{cart.coupon.code}</p>
                    <p className="text-xs text-green-600">
                      {cart.coupon.discountType === "percentage"
                        ? `${cart.coupon.discountValue}% off`
                        : `${formatPrice({
                            price: cart.coupon.discountValue,
                          })} off`}
                    </p>
                    {cart.couponDiscount > 0 && (
                      <p className="text-sm text-green-600 font-medium mt-1">
                        Discount: {formatPrice({ price: cart.couponDiscount })}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>

            <Button
              className="w-full"
              onClick={handleContinue}
              disabled={
                !selectedShippingId || (!isSameAddress && !selectedBillingId)
              }
            >
              Continue to Payment
            </Button>

            <Button
              variant="outline"
              className="w-full mt-2"
              onClick={() => router.push("/checkout")}
            >
              Back to Cart
            </Button>
          </div>
        </div>
      </div>
    </Wrapper>
  );
};

export default AddressPage;
