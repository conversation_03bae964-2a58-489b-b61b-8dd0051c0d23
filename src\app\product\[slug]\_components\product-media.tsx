"use client";
import * as React from "react";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Camera, Video } from "lucide-react";
import { getImageUrl } from "@/utils/image-url";
import { cn } from "@/lib/utils";

interface IProductMediaProps {
  gallery: string[];
  thumbnail: string;
  slug: string;
  setThumbnail: any;
}
export const ProductMedia: React.FC<IProductMediaProps> = ({
  gallery,
  thumbnail,
  slug,
  setThumbnail,
}) => {
  const mainImageRef = React.useRef<HTMLDivElement>(null);
  const [carouselHeight, setCarouselHeight] = React.useState<number | null>(
    null
  );

  React.useEffect(() => {
    if (mainImageRef.current) {
      const height = mainImageRef.current.clientHeight;
      setCarouselHeight(height);
    }

    // Optionally update on resize
    const handleResize = () => {
      if (mainImageRef.current) {
        setCarouselHeight(mainImageRef.current.clientHeight);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="space-x-1 w-full flex flex-col sm:grid sm:grid-cols-4 gap-1">
      <Carousel
        opts={{
          align: "start",
        }}
        orientation="vertical"
        className="w-full hidden sm:block"
      >
        <CarouselContent
          className={`-mt-1`}
          style={{ height: carouselHeight || 300 }}
        >
          {gallery.map((media, index) => (
            <CarouselItem
              key={index}
              className={cn(
                "pt-1 basis-1/3",
                gallery.length === 4 && "md:basis-1/4",
                gallery.length >= 5 && "md:basis-1/4 xl:basis-1/5"
              )}
              onClick={() => setThumbnail(media)}
            >
              <div className="bg-gray-200 p-5 cursor-pointer rounded-md">
                <Image
                  src={getImageUrl(media)}
                  alt="media"
                  width={700}
                  height={700}
                  priority={index == 0}
                  className="w-full h-full rounded-md hover:scale-110 transition-all duration-200 object-contain"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        {gallery.length >= 5 && <CarouselPrevious />}
        {gallery.length >= 5 && <CarouselNext />}
      </Carousel>

      <div className="aspect-[1.2/1] bg-gray-200 rounded-md p-4 w-full col-span-3">
        <div
          ref={mainImageRef}
          className="flex flex-col justify-between h-full"
        >
          <Image
            src={getImageUrl(thumbnail)}
            width={1000}
            height={1000}
            alt="thumbnail"
            className="w-full h-full object-contain"
          />
          <div className="flex  justify-between w-full h-full flex-1">
            <Button
              size={"sm"}
              className="rounded-md p-0 transition-all duration-300 bg-primary/20 text-primary hover:text-white text-sm"
            >
              <Link
                className="flex items-center gap-2 cursor-pointer px-2 py-1"
                href={`/product/${slug}/tryon`}
              >
                <Video />
                Try On
              </Link>
            </Button>
            <Button
              size={"sm"}
              className="rounded-md p-0 transition-all duration-300 bg-gray-300 text-gray-600 hover:text-white text-sm"
            >
              <Link
                className="flex items-center gap-2 cursor-pointer px-2 py-1"
                href={`/product/${slug}/tryon`}
              >
                <Camera />
                Try On
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full block sm:hidden"
      >
        <CarouselContent className={`-ml-1`}>
          {gallery.map((media, index) => (
            <CarouselItem key={index} className="pl-1 basis-1/3">
              <div className="bg-gray-200 p-5 cursor-pointer rounded-md">
                <Image
                  src={getImageUrl(media)}
                  alt="media"
                  width={700}
                  height={700}
                  priority={index == 0}
                  className="w-full h-full rounded-md hover:scale-110 transition-all duration-200 object-contain"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </div>
  );
};
