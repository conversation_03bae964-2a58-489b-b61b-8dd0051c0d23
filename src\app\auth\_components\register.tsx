"use client";

import { useEffect, useState } from "react";
import { <PERSON>, EyeOff, MoveRight } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import PhoneInput from "react-phone-input-2";
import { parsePhoneNumberFromString } from "libphonenumber-js";
import "react-phone-input-2/lib/style.css";
import store, { RootState } from "@/store/store";
import { registerUser, resetAuthState } from "@/store/slices/auth-slice";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { ToastErrors } from "@/components/common/toast-errors";

const registerSchema = z
  .object({
    fullName: z.string().min(2, { message: "Name is required" }),
    email: z.string().email({ message: "Invalid email" }),
    phone: z.string().refine(
      (val) => {
        const phone = parsePhoneNumberFromString(
          val.startsWith("+") ? val : `+${val}`
        );
        return phone?.isValid();
      },
      {
        message: "Enter a valid phone number",
      }
    ),
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z.string(),
    termsAccepted: z.literal(true, {
      errorMap: () => ({
        message: "You must accept the terms and conditions.",
      }),
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type RegisterFormData = z.infer<typeof registerSchema>;

export default function RegisterForm() {
  const router = useRouter();
  const { loading, error, success } = useSelector(
    (state: RootState) => state.auth
  );
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    trigger,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  // Register manually since react-phone-input-2 isn't a native input
  useEffect(() => {
    register("phone");
  }, [register]);

  useEffect(() => {
    if (success) {
      router.push("/");
    }
  }, [success]);

  useEffect(() => {
    if (error?.length || success) {
      store.dispatch(resetAuthState());
    }
  }, [error, success]);

  const onSubmit = (data: RegisterFormData) => {
    store.dispatch(
      registerUser({
        email: data.email,
        phone: data.phone,
        password: data.password,
        fullName: data.fullName,
      })
    );
  };

  return (
    <>
      <ToastErrors error={error} success={success} />
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">Full Name</label>
          <input
            type="text"
            {...register("fullName")}
            placeholder="John Doe"
            className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm"
          />
          {errors.fullName && (
            <p className="text-sm text-red-500 mt-1">
              {errors.fullName.message}
            </p>
          )}
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">Email</label>
          <Input
            type="email"
            {...register("email")}
            placeholder="<EMAIL>"
            className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm"
          />
          {errors.email && (
            <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
          )}
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">Phone Number</label>

          <PhoneInput
            country="in"
            value={watch("phone")}
            onChange={(value) => {
              setValue("phone", value);
              trigger("phone");
            }}
            inputProps={{
              name: "phone",
              className:
                "w-full border border-gray-200 rounded-md px-12 py-2 text-sm",
            }}
            inputClass="!w-full !pl-12"
            containerClass="!w-full"
            enableSearch
          />

          {errors.phone && (
            <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
          )}
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">Password</label>
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              {...register("password")}
              placeholder="••••••••"
              className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            />

            <Button
              type="button"
              size={"icon"}
              className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
              onClick={() => setShowPassword((prev) => !prev)}
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </Button>
          </div>
          {errors.password && (
            <p className="text-sm text-red-500 mt-1">
              {errors.password.message}
            </p>
          )}
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium">
            Confirm Password
          </label>
          <div className="relative">
            <Input
              type={showConfirm ? "text" : "password"}
              {...register("confirmPassword")}
              placeholder="••••••••"
              className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm pr-10"
            />

            <Button
              type="button"
              size={"icon"}
              className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 p-0 bg-transparent shadow-none hover:bg-transparent"
              onClick={() => setShowConfirm((prev) => !prev)}
            >
              {showConfirm ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </Button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-red-500 mt-1">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        <div className="flex gap-2 mb-6 items-center">
          <Input
            type="checkbox"
            {...register("termsAccepted")}
            id="termsAccepted"
            className="checked:bg-primary"
          />

          <label htmlFor="termsAccepted" className="text-sm text-gray-700">
            I agree to the{" "}
            <Link
              href="/terms"
              target="_blank"
              className="text-blue-400 hover:text-blue-600 font-semibold"
            >
              Terms and Conditions
            </Link>{" "}
            and{" "}
            <Link
              href="/terms"
              target="_blank"
              className="text-blue-400 hover:text-blue-600 font-semibold"
            >
              Privacy Policy
            </Link>
          </label>
        </div>

        <Button
          disabled={isSubmitting || loading}
          size={"lg"}
          className="w-full py-4"
        >
          {isSubmitting || loading ? (
            "Registering..."
          ) : (
            <>
              Register <MoveRight />{" "}
            </>
          )}
        </Button>

        {/* <Separator /> */}

        {/* <div className="space-y-3">
          <Button
            type="button"
            className="w-full flex items-center justify-center gap-2 transition bg-white text-text-accent shadow-none border border-gray-200 h-11 hover:bg-gray-50 hover:text-text-accent"
          >
            <img src="/google.svg" alt="Google" className="w-5 h-5" />
            Continue with Google
          </Button>

          <Button
            type="button"
            className="w-full flex items-center justify-center gap-2 transition bg-white text-text-accent shadow-none border border-gray-200 h-11 hover:bg-gray-50 hover:text-text-accent"
          >
            <img src="/apple.svg" alt="Apple" className="w-5 h-5" />
            Continue with Apple
          </Button>
        </div> */}
      </form>
    </>
  );
}
