"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { MoveRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { NewsLetter } from "@/app/(home)/_components/newsletter";
import store, { RootState } from "@/store/store";
import { forgotPassword, resetAuthState } from "@/store/slices/auth-slice";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { ToastErrors } from "@/components/common/toast-errors";

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: "Invalid email" }),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function LoginForm() {
  const { error, success, loading } = useSelector(
    (state: RootState) => state.auth
  );
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  useEffect(() => {
    if (error?.length || success) {
      store.dispatch(resetAuthState());
    }

    if (success) {
      reset();
    }
  }, [error, success]);

  const onSubmit = (data: ForgotPasswordFormData) => {
    store.dispatch(forgotPassword({ email: data.email }));
  };

  return (
    <div className="space-y-6">
      <ToastErrors error={error} success={success} />
      <div className="flex items-center justify-center px-4">
        <div
          className={`w-full max-w-md sm:max-w-lg lg:max-w-xl space-y-6 bg-white rounded-md shadow py-8 mt-28`}
        >
          <div className="px-6 sm:px-8">
            <h2 className="text-center text-xl font-semibold mb-3">
              Forgot Password
            </h2>

            <p className="text-center text-sm mb-6">
              Enter the email address associated with your account.
            </p>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mb-6">
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  Email Address
                </label>

                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email")}
                  className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm"
                />

                {errors.email && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <Button
                disabled={isSubmitting}
                size={"lg"}
                className="w-full py-4 font-semibold uppercase mb-6"
              >
                {isSubmitting ? (
                  "Loading..."
                ) : (
                  <>
                    Forgot password <MoveRight />{" "}
                  </>
                )}
              </Button>
            </form>

            <p className="mb-2 text-sm">
              Already have an account?{" "}
              <Link className="text-blue-400 font-semibold ml-1" href={"/auth"}>
                Sign In
              </Link>
            </p>

            <p className="mb-2 text-sm">
              Don't have an account?{" "}
              <Link
                className="text-blue-400 font-semibold ml-1"
                href={"/auth?t=r"}
              >
                Sign Up
              </Link>
            </p>

            <Separator />

            <p className="text-sm">
              You may contact{" "}
              <Link href={"/help"} className="text-primary">
                Customer Service
              </Link>{" "}
              for help restoring access to your account.
            </p>
          </div>
        </div>
      </div>

      <NewsLetter />
    </div>
  );
}
