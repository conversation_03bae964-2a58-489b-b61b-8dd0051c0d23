"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Info, MoveRight } from "lucide-react";
import React from "react";

const Page = () => {
  return (
    <div className="py-7 space-y-6">
      <h2 className="font-semibold text-2xl">Track Order</h2>

      <p>
        To track your order please enter your order ID in the input field below
        and press the “Track Order” button. this was given to you on your
        receipt and in the confirmation email you should have received.
      </p>

      <div className="flex space-x-6">
        <Input
          label="Order ID"
          type="text"
          placeholder="ID..."
          containerClassName="w-1/4"
          className="border-gray-200 placeholder-gray-400"
        />

        <Input
          label="Billing Email"
          type="email"
          placeholder="Email address"
          containerClassName="w-1/4"
          className="border-gray-200 placeholder-gray-400"
        />
      </div>

      <p className="flex text-xs items-center text-text-accent">
        <Info size={16} className="mr-1" />
        Order ID that we sended to your in your email address.
      </p>

      <Button className="rounded-none px-6 mt-8">
        Track Order
        <MoveRight />
      </Button>
    </div>
  );
};

export default Page;
