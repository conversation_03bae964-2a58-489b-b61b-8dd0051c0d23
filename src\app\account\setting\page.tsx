"use client";

import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import parsePhoneNumberFromString from "libphonenumber-js";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import PhoneInput from "react-phone-input-2";
import { z } from "zod";
import "react-phone-input-2/lib/style.css";
import { Select } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { updateProfile } from "@/store/slices/user-slice";
import { ToastErrors } from "@/components/common/toast-errors";
import { resetAuthState } from "@/store/slices/auth-slice";

const accountSchema = z.object({
  fullName: z.string().min(1, "Please enter full name"),
  email: z.string().email({ message: "Invalid email" }),
  phone: z.string().refine(
    (val) => {
      const phone = parsePhoneNumberFromString(
        val.startsWith("+") ? val : `+${val}`
      );
      return phone?.isValid();
    },
    {
      message: "Enter a valid phone number",
    }
  ),
});

type AccountFormData = z.infer<typeof accountSchema>;

const Page = () => {
  const { user, error, loading, success } = useSelector(
    (state: RootState) => state.user
  );

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    trigger,
    formState: { errors, isSubmitting },
  } = useForm<AccountFormData>({
    resolver: zodResolver(accountSchema),
  });

  useEffect(() => {
    register("phone");
  }, [register]);

  useEffect(() => {
    if (user) {
      setValue("email", user?.email);
      setValue("fullName", user?.fullName);
      setValue("phone", user?.phone);
    }
    return () => {};
  }, [user]);

  useEffect(() => {
    if (error?.length || success) {
      store.dispatch(resetAuthState());
    }
  }, [error, success]);

  const onSubmit = (data: AccountFormData) => {
    store.dispatch(updateProfile(data));
  };

  return (
    <div className="space-y-3">
      <ToastErrors error={error} success={success} />
      {/* ACCOUNT */}
      <div className="border border-gray-200 rounded-sm">
        <h2 className="uppercase border-b border-gray-200 py-4 px-6 font-semibold">
          Account Setting
        </h2>

        <div className="flex p-6">
          <div className="w-1/6 flex items-start justify-center">
            <img src="/user.png" className="w-44" alt="" />
          </div>

          <div className="w-full">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid pl-6 gap-x-4">
                {/* FULL NAME */}
                <div className="mb-4 col-span-6">
                  <label className="block text-sm font-medium mb-2">
                    Full Name
                  </label>

                  <Input
                    type="text"
                    placeholder="Full Name"
                    {...register("fullName")}
                    className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm"
                  />

                  {errors.fullName && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.fullName.message}
                    </p>
                  )}
                </div>

                {/* Email */}
                <div className="mb-4 col-span-6">
                  <label className="block text-sm font-medium mb-2">
                    Email
                  </label>

                  <Input
                    type="text"
                    placeholder="Email"
                    {...register("email")}
                    className="w-full border border-gray-200 rounded-md px-3 py-2 text-sm"
                  />

                  {errors.email && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                {/* PHONE NUMBER */}
                <div className="mb-4 col-span-6">
                  <label className="block mb-2 text-sm font-medium">
                    Phone Number
                  </label>

                  <PhoneInput
                    country="in"
                    value={watch("phone")}
                    onChange={(value) => {
                      setValue("phone", value);
                      trigger("phone");
                    }}
                    inputProps={{
                      name: "phone",
                      className:
                        "w-full border border-gray-200 rounded-md px-12 py-2 text-sm",
                    }}
                    inputClass="!w-full !pl-12"
                    containerClass="!w-full"
                    enableSearch
                  />

                  {errors.phone && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.phone.message}
                    </p>
                  )}
                </div>
              </div>

              <Button
                disabled={isSubmitting}
                type="submit"
                className="transition ml-6 mt-4"
              >
                {isSubmitting || loading ? "Updating..." : "Save Changes"}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
