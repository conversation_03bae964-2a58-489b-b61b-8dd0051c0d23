import { I<PERSON><PERSON> } from "./brand";
import { ICategory } from "./category";
import { IEyeWear } from "./eye-wear";
import { IFrameShape } from "./frame-shape";
import { ISubCategory } from "./sub-category";
import { IVariation } from "./variations";

export interface IProduct {
  _id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  specifications: string;
  category: string | ICategory;
  subCategory: ISubCategory;
  brand: IBrand;
  basePrice: number;
  mrp: number;
  stock: number;
  weight: number;
  unlimited: boolean;
  ratings?: number;
  thumbnail?: string;
  images: string[];
  variations: IVariation[];
  frameShape: IFrameShape;
  eyeWear: IEyeWear;
  attributes: {
    [key: string]: string[];
  };
}
