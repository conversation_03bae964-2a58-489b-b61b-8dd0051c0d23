// REDUX
import { combineReducers } from "redux";
import { useDispatch } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";

// SLICES
import categorySlice from "./slices/category-slice";
import authSlice from "./slices/auth-slice";
import brandSlice from "./slices/brand-slice";
import bannerSlice from "./slices/banner-slice";
import cartSlice from "./slices/cart-slice";
import wishlistSlice from "./slices/wishlist-slice";
import userSlice from "./slices/user-slice";
import eyeWearSlice from "./slices/eye-wear-slice";
import frameShapeSlice from "./slices/frame-shape-slice";
import productSlice from "./slices/product-slice";
import attributeSlice from "./slices/attribute-slice";
import addressSlice from "./slices/address-slice";
import checkoutSlice from "./slices/checkout-slice";
import orderSlice from "./slices/order-slice";
import taxSlice from "./slices/tax-slice";
import lensCategorySlice from "./slices/lens-category-slice";
import lensPackageSlice from "./slices/lens-package-slice";

const reducers = combineReducers({
  category: categorySlice,
  auth: authSlice,
  brand: brandSlice,
  banner: bannerSlice,
  cart: cartSlice,
  wishlist: wishlistSlice,
  user: userSlice,
  eyeWear: eyeWearSlice,
  frameShape: frameShapeSlice,
  product: productSlice,
  attribute: attributeSlice,
  address: addressSlice,
  checkout: checkoutSlice,
  order: orderSlice,
  tax: taxSlice,
  lensCategory: lensCategorySlice,
  lensPackage: lensPackageSlice,
});

export type RootState = ReturnType<typeof reducers>;

const store = configureStore({
  reducer: reducers,
});

export type AppDispatch = typeof store.dispatch;
export const useAppDispatch: () => AppDispatch = useDispatch;
export default store;
