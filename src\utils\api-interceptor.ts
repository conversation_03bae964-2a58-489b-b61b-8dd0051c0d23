import { BASE_URL } from "@/constants/url";
import axios from "axios";

export const api_interceptor = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
});

// Add a request interceptor
api_interceptor.interceptors.request.use(
  (config) => {
    // Get token from localStorage if available
    const token =
      typeof window !== "undefined" ? localStorage.getItem("token") : null;
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
api_interceptor.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);
