// REDUX
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// INTERFACE
import { IProduct, IProductSlice, IPaginator } from "@/interfaces";

// CONSTANTS
import { PRODUCT_URL } from "@/constants/url";

// UTILS
import { api_interceptor } from "@/utils/api-interceptor";
import { FetchDataRejected } from "@/interfaces/reject-type";

const initialState: IProductSlice = {
  products: [],
  paginator: {
    totalPages: 0,
    limit: 10,
    page: 1,
    total: 0,
  },
  loading: true,
  error: "",
};

export const fetchProducts = createAsyncThunk<
  { products: IProduct[]; paginator: IPaginator },
  {
    page?: number;
    limit?: number;
    frameShape?: string[];
    eyeWear?: string[];
    attribute?: string[];
    category?: string[];
    sort?: string;
  },
  FetchDataRejected
>(
  "products/fetch",
  async (
    {
      page = 1,
      limit = 10,
      frameShape = [],
      eyeWear = [],
      attribute = [],
      sort = "newest",
      category = [],
    },
    { rejectWithValue }
  ) => {
    const query = new URLSearchParams();
    query.append("page", String(page));
    query.append("limit", String(limit));
    query.append("frameShape", frameShape.join(","));
    query.append("eyeWear", eyeWear.join(","));
    query.append("attribute", attribute.join(","));
    query.append("category", category.join(","));
    query.append("sort", sort);

    return api_interceptor
      .get(PRODUCT_URL + `?${query}`)
      .then((res) => {
        const { products, paginator } = res.data.data;
        return { products, paginator };
      })
      .catch((err) => {
        return rejectWithValue({ message: err.message || err });
      });
  }
);

const productsSlice = createSlice({
  name: "product",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchProducts.pending, (state, { meta: { arg } }) => {
      if (arg?.page === 1) state.products = [];
      state.loading = true;
      state.error = "";
    });
    builder.addCase(fetchProducts.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.paginator = payload.paginator;
      state.products = payload.products;
    });
    builder.addCase(fetchProducts.rejected, (state, { payload }) => {
      state.loading = false;
      state.error = payload?.message || "Something went wrong";
    });
  },
});

export default productsSlice.reducer;
