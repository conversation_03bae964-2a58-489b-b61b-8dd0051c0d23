import { ILensPackage } from "./lens-package";
import { IProduct } from "./product";
import { IVariation } from "./variations";

interface ILensPackageCart {}

export interface ILensData {
  visionType: string;
  packageId: string;
  packagePrice: number;
  prescriptionType: string;
  prescription?: {
    name: string;
    pd: string;
    rightEye: {
      sph: string;
      cyl: string;
      axis: string;
      add: string;
    };
    leftEye: {
      sph: string;
      cyl: string;
      axis: string;
      add: string;
    };
  };
  lensCategory: string;
  lensPackage: {
    name: string;
    price: number;
    _id: string;
    subPackage?: { title: string; price: number } | null;
  };
  totalPrice: number;
}

export interface ICartItem {
  id: string;
  quantity: number;
  product: Omit<IProduct, "variations">;
  variation?: IVariation;
  lensData?: ILensData;
}
