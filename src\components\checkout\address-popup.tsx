"use client";

import React from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { AddressForm } from "../address/address-form";

interface AddressPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AddressPopup: React.FC<AddressPopupProps> = ({
  isOpen,
  onClose,
}) => {
  const onCancel = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px]">
        <AddressForm onCancel={onCancel} />
      </DialogContent>
    </Dialog>
  );
};
