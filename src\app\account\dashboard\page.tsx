"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { RootState } from "@/store/store";
import { Package, ReceiptTextIcon, RocketIcon } from "lucide-react";
import Link from "next/link";
import React from "react";
import { useSelector } from "react-redux";

const Page = () => {
  const { user } = useSelector((state: RootState) => state.user);
  return (
    <div className="space-y-3">
      <h2 className="text-xl font-semibold">Hello, {user?.fullName}</h2>

      <p className="text-sm">
        From your account dashboard. you can easily check & view your
        <br />
        <Link href={"/account/orders"} className="text-blue-500">
          Recent Orders
        </Link>
        , manage your{" "}
        <Link href={"/account/address"} className="text-blue-500">
          Shipping and Billing Addresses
        </Link>{" "}
        and <br /> edit your{" "}
        <Link href={"/account/reset-password"} className="text-blue-500">
          Password
        </Link>{" "}
        and{" "}
        <Link href={"/account/setting"} className="text-blue-500">
          Account Details
        </Link>
        .
      </p>

      <div className="grid gap-6">
        <div className="border border-gray-200 rounded-sm h-fit">
          <div className="border-b border-gray-200">
            <h2 className="px-6 py-4 uppercase text-sm font-semibold">
              Account Info
            </h2>
          </div>

          <div className="space-y-6 px-6 py-4">
            <div className="flex">
              <img className="mr-4" src="/user.png" alt="" />

              <div className="space-y-1">
                <p className="font-semibold">{user?.fullName}</p>

                <p className="text-gray-400">{user?.fullName}</p>
              </div>
            </div>

            <div className="space-y-2">
              <p>
                Email: <span className="text-gray-400">{user?.email}</span>
              </p>
              {/* <p>
                Sec Email:{" "}
                <span className="text-gray-400"><EMAIL></span>
              </p> */}
              <p>
                Phone: <span className="text-gray-400">+{user?.phone}</span>
              </p>
            </div>

            <Button className="rounded-none bg-white border-blue-100 text-blue-400 shadow-none border uppercase font-semibold hover:bg-blue-400 hover:text-white p-0">
              <Link href={"/account/setting"} className="py-2 px-4">
                Edit Account
              </Link>
            </Button>
          </div>
        </div>

        {/* <div className="col-span-1 xl:col-span-2 border border-gray-200 rounded-sm h-fit">
          <div className="border-b border-gray-200">
            <h2 className="px-6 py-4 uppercase text-sm font-semibold">
              Billing Address
            </h2>
          </div>

          <div className="px-6 py-4">
            <div className="space-y-6">
              <p className="font-semibold">Arpit Nagpal</p>

              <p className="text-gray-400">
                East Tejturi Bazar, Word No. 04, Road No. 13/x, House no.
                1320/C, Flat No. 5D, Dhaka - 1200, Bangladesh
              </p>

              <p>
                Phone: <span className="text-gray-400">+91 94787-04255</span>
              </p>

              <p>
                Email:{" "}
                <span className="text-gray-400"><EMAIL></span>
              </p>

              <Button className="rounded-none bg-white border-blue-100 text-blue-400 shadow-none border uppercase font-semibold hover:bg-blue-400 hover:text-white p-0">
                <Link href={"/account/address"} className="py-2 px-4">
                  Edit Address
                </Link>
              </Button>
            </div>
          </div>
        </div> */}

        {/* <div className="space-y-6">
          <Link href={"/account/orders"} className="block">
            <div className="flex items-center bg-blue-100 p-4 rounded-sm cursor-pointer">
              <div className="mr-4 bg-white p-3">
                <RocketIcon className="text-blue-400 -rotate-45" />
              </div>

              <div className="space-y-1">
                <h2 className="text-xl font-semibold">154</h2>
                <p className="text-sm">Total Orders</p>
              </div>
            </div>
          </Link>

          <Link href={"/account/orders"} className="block">
            <div className="flex items-center bg-orange-100 p-4 rounded-sm cursor-pointer">
              <div className="mr-4 bg-white p-3">
                <ReceiptTextIcon className="text-orange-400" />
              </div>

              <div className="space-y-1">
                <h2 className="text-xl font-semibold">05</h2>
                <p className="text-sm">Pending Orders</p>
              </div>
            </div>
          </Link>

          <Link href={"/account/orders"} className="block">
            <div className="flex items-center bg-green-100 p-4 rounded-sm cursor-pointer">
              <div className="mr-4 bg-white p-3">
                <Package className="text-green-400" />
              </div>

              <div className="space-y-1">
                <h2 className="text-xl font-semibold">149</h2>
                <p className="text-sm">Completed Orders</p>
              </div>
            </div>
          </Link>
        </div> */}
      </div>
    </div>
  );
};

export default Page;
